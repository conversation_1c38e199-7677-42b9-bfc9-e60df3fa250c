# Static Analysis Guide for Argus SNMP Trap Receiver

This document describes the comprehensive static analysis setup for the Argus SNMP trap receiver project.

## Overview

The project uses multiple static analysis tools to ensure code quality, security, and maintainability:

### Primary Tools (Integrated in CI)

- **golangci-lint** - Comprehensive linting with 20+ linters
- **gosec** - Security vulnerability scanner
- **govulncheck** - Go vulnerability database scanner

### Extended Tools (Additional Analysis)

- **staticcheck** - Advanced static analysis
- **ineffassign** - Ineffectual assignment detection
- **gocyclo** - Cyclomatic complexity analysis
- **dupl** - Code duplication detection

## Quick Start

### Running All Analysis Tools

```bash
# Install all tools and run comprehensive analysis
make analysis

# Or use the dedicated script
./scripts/static-analysis.sh
```

### Running Individual Tools

```bash
# Linting
make lint

# Security analysis
make security

# Specific tools
make staticcheck
make ineffassign
make gocyclo
make dupl
```

## Tool Configuration

### golangci-lint Configuration

The project uses a comprehensive `.golangci.yml` configuration with:

- **Enabled linters**: depguard, errorlint, gocritic, godot, loggercheck, misspell, nilnesserr, perfsprint, predeclared, revive, sloglint, testifylint, unconvert, unused, usestdlibvars, whitespace
- **Custom rules**: Enforces slog usage, prevents deprecated packages
- **Exclusions**: Reasonable exclusions for test files and generated code

### staticcheck Configuration

Custom `.staticcheck.conf` with:

- All checks enabled by default
- SNMP-specific naming conventions allowed
- Performance-focused checks enabled

### Security Tools

- **gosec**: Scans for common security issues
- **govulncheck**: Checks against Go vulnerability database

## CI/CD Integration

### GitLab CI Stages

1. **lint** - golangci-lint with code quality reports
2. **security** - gosec and govulncheck
3. **static-analysis** - Extended analysis with all tools

### Artifacts Generated

- `reports/golangci-lint.json` - Code quality report
- `reports/gosec.json` - Security analysis
- `reports/govulncheck.json` - Vulnerability scan
- `reports/staticcheck.json` - Advanced static analysis
- `reports/ineffassign.txt` - Ineffectual assignments
- `reports/gocyclo.txt` - Complexity analysis
- `reports/dupl.txt` - Code duplication
- `coverage/coverage.html` - Test coverage report

## Tool Details

### golangci-lint

**Purpose**: Comprehensive code quality analysis
**Configuration**: `.golangci.yml`
**Key Features**:

- 20+ integrated linters
- Custom rules for slog usage
- Code quality metrics
- IDE integration support

**Example Issues Detected**:

- Unused variables/imports
- Inefficient string operations
- Missing error handling
- Code style violations

### gosec

**Purpose**: Security vulnerability detection
**Key Features**:

- SQL injection detection
- Hardcoded credentials
- Weak cryptography usage
- File path traversal

**Example Issues Detected**:

- Use of weak random number generators
- Potential command injection
- Insecure file permissions
- Hardcoded secrets

### govulncheck

**Purpose**: Known vulnerability scanning
**Key Features**:

- Go vulnerability database integration
- Dependency vulnerability scanning
- Call graph analysis
- Actionable remediation advice

### staticcheck

**Purpose**: Advanced static analysis
**Configuration**: `.staticcheck.conf`
**Key Features**:

- Deep code analysis
- Performance issue detection
- API usage validation
- Code smell detection

**Example Issues Detected**:

- Unreachable code
- Inefficient operations
- API misuse
- Potential bugs

### ineffassign

**Purpose**: Ineffectual assignment detection
**Key Features**:

- Dead code detection
- Unused assignments
- Variable shadowing issues

### gocyclo

**Purpose**: Cyclomatic complexity analysis
**Threshold**: 10 (configurable)
**Key Features**:

- Function complexity measurement
- Maintainability assessment
- Refactoring recommendations

### dupl

**Purpose**: Code duplication detection
**Threshold**: 50 tokens (configurable)
**Key Features**:

- Duplicate code blocks
- Copy-paste detection
- Refactoring opportunities

## Best Practices

### Development Workflow

1. **Pre-commit**: Run `make lint` before committing
2. **Pre-push**: Run `make analysis` before pushing
3. **Code Review**: Address all static analysis findings
4. **CI/CD**: Ensure all checks pass in pipeline

### Handling Findings

#### False Positives

Use appropriate exclusions in tool configurations:

```yaml
# .golangci.yml
issues:
  exclude-rules:
    - linters: [specific-linter]
      text: "specific pattern to exclude"
```

#### Security Issues

- **High Priority**: Fix immediately
- **Medium Priority**: Fix in current sprint
- **Low Priority**: Create technical debt ticket

#### Code Quality Issues

- **Critical**: Block merge
- **Major**: Fix before merge
- **Minor**: Create improvement ticket

### Performance Considerations

- **Local Development**: Use `make lint` for quick feedback
- **CI/CD**: Full analysis runs automatically
- **Caching**: Tools and dependencies are cached in CI

## Troubleshooting

### Common Issues

#### Tool Installation Failures

```bash
# Clean and reinstall tools
make clean
make install-tools
```

#### Memory Issues in CI

- Increase CI runner memory allocation
- Use tool-specific memory limits
- Run analysis in parallel stages

#### False Positives detection

- Review tool configurations
- Add appropriate exclusions
- Update tool versions

### Debug Commands

```bash
# Verbose output
golangci-lint run -v

# Specific linter only
golangci-lint run --disable-all --enable=gosec

# Tool version information
staticcheck -version
gosec -version
```

## Metrics and Reporting

### Code Quality Metrics

- **Linting Issues**: Target 0 issues
- **Test Coverage**: Target >80%
- **Cyclomatic Complexity**: Target <10 per function
- **Code Duplication**: Target <5%

### Security Metrics

- **Security Issues**: Target 0 high/critical issues
- **Vulnerabilities**: Target 0 known vulnerabilities
- **Dependency Health**: Regular updates and scanning

### Reporting

Reports are generated in JSON and text formats for:

- CI/CD integration
- IDE integration
- Manual review
- Trend analysis

## Integration with IDEs

### VS Code

Install the golangci-lint extension for real-time feedback.

### GoLand/IntelliJ

Configure external tools for each static analysis tool.

### Vim/Neovim

Use ALE or similar plugins for linting integration.

## Continuous Improvement

### Regular Updates

- Update tool versions monthly
- Review and update configurations quarterly
- Assess new tools and linters annually

### Metrics Tracking

- Monitor trend of issues over time
- Track resolution time for findings
- Measure impact on code quality

### Team Training

- Regular training on tool usage
- Best practices documentation
- Code review guidelines
