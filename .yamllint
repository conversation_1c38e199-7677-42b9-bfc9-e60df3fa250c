# .yamllint configuration for best practices
extends: default

rules:
  line-length:
    max: 120
    level: warning
  indentation:
    spaces: 2
    indent-sequences: consistent
  trailing-spaces: enable
  new-line-at-end-of-file: enable
  truthy:
    allowed-values: ['true', 'false']
  colons:
    max-spaces-before: 0
    max-spaces-after: 1
  hyphens:
    max-spaces-after: 1
  document-start: enable
  empty-lines:
    max: 2
  comments:
    require-starting-space: true
