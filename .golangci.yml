formatters:
  enable:
    - gci
    - gofumpt
    - goimports
  settings:
    gci:
      sections:
        - standard
        - default
        - prefix(gitlab-stel.prosdis.118-vaud.ch/stel/infra/supervision/argus/argus-app)
    gofumpt:
      extra-rules: true
    goimports:
      local-prefixes:
        - gitlab-stel.prosdis.118-vaud.ch/stel/infra/supervision/argus/argus-app

issues:
  max-issues-per-linter: 0
  max-same-issues: 0

linters:
  enable:
    - depguard
    - errorlint
    - exptostd
    - gocritic
    - godot
    - loggercheck
    - misspell
    - nilnesserr
    - perfsprint
    - predeclared
    - revive
    - sloglint
    - testifylint
    - unconvert
    - unused
    - usestdlibvars
    - whitespace

  exclusions:
    paths:
      - ^.*\.(pb|y)\.go$
    rules:
      - linters:
          - errcheck
        text: Error return value of .((os\.)?std(out|err)\..*|.*Close|.*Flush|os\.Remove(All)?|.*print(f|ln)?|os\.(Un)?Setenv). is not checked
      - linters:
          - govet
        text: "stdmethods: method Seek.* should have signature Seek"
      - linters:
          - revive
        text: exported (.+) should have comment( \(or a comment on this block\))? or be unexported
      - linters:
          - gocritic
        text: "appendAssign"
      - linters:
          - errcheck
        path: _test.go
      - linters:
          - errorlint
        path: "tsdb/head_wal.go"
      - linters:
          - godot
        source: "^// ==="
    warn-unused: true
  settings:
    depguard:
      rules:
        main:
          deny:
            - pkg: "sync/atomic"
              desc: "Use go.uber.org/atomic instead of sync/atomic"
            - pkg: "github.com/stretchr/testify/assert"
              desc: "Use github.com/stretchr/testify/require instead of github.com/stretchr/testify/assert"
            - pkg: "github.com/go-kit/kit/log"
              desc: "Use github.com/go-kit/log instead of github.com/go-kit/kit/log"
            - pkg: "io/ioutil"
              desc: "Use corresponding 'os' or 'io' functions instead."
            - pkg: "regexp"
              desc: "Use github.com/grafana/regexp instead of regexp"
            - pkg: "github.com/pkg/errors"
              desc: "Use 'errors' or 'fmt' instead of github.com/pkg/errors"
            - pkg: "gzip"
              desc: "Use github.com/klauspost/compress instead of gzip"
            - pkg: "zlib"
              desc: "Use github.com/klauspost/compress instead of zlib"
            - pkg: "golang.org/x/exp/slices"
              desc: "Use 'slices' instead."
    errcheck:
      exclude-functions:
        - io.Copy
        - io.WriteString
        - (net/http.ResponseWriter).Write
        - (*net/http.Server).Shutdown
    perfsprint:
      int-conversion: true
      err-error: true
      errorf: true
      sprintf1: true
      strconcat: false
    revive:
      rules:
        - name: blank-imports
        - name: comment-spacings
        - name: context-as-argument
          arguments:
            - allowTypesBefore: '*testing.T,testing.TB'
        - name: context-keys-type
        - name: dot-imports
        - name: early-return
          arguments:
            - "preserveScope"
        - name: empty-block
          disabled: true
        - name: error-naming
        - name: error-return
        - name: error-strings
        - name: errorf
        - name: exported
        - name: increment-decrement
        - name: indent-error-flow
          arguments:
            - "preserveScope"
        - name: package-comments
          disabled: true
        - name: range
        - name: receiver-naming
        - name: redefines-builtin-id
        - name: superfluous-else
          arguments:
            - "preserveScope"
        - name: time-naming
        - name: unexported-return
        - name: unreachable-code
        - name: unused-parameter
        - name: var-declaration
        - name: var-naming
    testifylint:
      disable:
        - float-compare
        - go-require
      enable-all: true

output:
  show-stats: false

run:
  timeout: 15m

version: "2"