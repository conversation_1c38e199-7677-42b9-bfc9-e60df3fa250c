# Argus SNMP Trap Receiver - Development Guidelines

This document provides project-specific development practices and procedures for the Argus SNMP trap receiver. It targets experienced Go developers familiar with SNMP protocols.

## Build & Configuration Setup

### Dependencies & Environment

**Go Version**: 1.24.3 (as specified in `.gitlab-ci.yml`)

**Key Dependencies**:

- `github.com/gosnmp/gosnmp v1.37.0` - SNMP protocol implementation
- `github.com/rs/zerolog v1.32.0` - Structured logging (migrated from standard log)
- `github.com/spf13/viper v1.18.2` - Configuration management
- `cuelang.org/go v0.7.0` - Template engine for message formatting

### Build Commands

```bash
# Standard build (from project root)
go build -o argus ./cmd/argus

# Build with version information (production)
go build -ldflags "-X main.version=v1.0.0 -X main.commit=$(git rev-parse HEAD) -X main.date=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o argus ./cmd/argus

# Cross-compilation examples
GOOS=linux GOARCH=amd64 go build -o argus-linux-amd64 ./cmd/argus
GOOS=windows GOARCH=amd64 go build -o argus-windows-amd64.exe ./cmd/argus

# Optimized build (used in CI)
CGO_ENABLED=0 go build -o argus ./cmd/argus
```

### Configuration Structure

The application uses a hierarchical configuration system with the following precedence:

1. Command-line flags (highest priority)
2. Environment variables (prefixed with `ARGUS_`)
3. Configuration file (YAML format)
4. Default values (lowest priority)

**Key Configuration Sections**:

- `server`: Port and bind address settings
- `snmp`: SNMP version, community, MIB paths
- `output`: stdout and Elasticsearch output configuration
- `templates`: Cue template paths and defaults
- `logging`: Log level and output configuration
- `health`: Health check endpoint configuration

**Example Configuration**:

```yaml
server:
  port: 162
  bind_address: "0.0.0.0"
snmp:
  version: "2c"
  community: "public"
  mib_paths: ["/usr/share/snmp/mibs", "./mibs"]
output:
  stdout:
    enabled: true
    format: "text"  # text, json, logfmt
logging:
  level: "info"
```

### Environment Variables

All configuration keys can be overridden using environment variables:

```bash
export ARGUS_SERVER_PORT=1162
export ARGUS_SNMP_COMMUNITY=private
export ARGUS_OUTPUT_STDOUT_FORMAT=json
```

## Testing Framework & Procedures

### Test Structure

The project uses Go's standard testing framework with table-driven tests. Current test coverage includes:

**Existing Test Packages**:

- `internal/errors` - Input validation and error handling
- `internal/snmp` - SNMP parsing, OID translation, and message formatting

### Running Tests

```bash
# Run all tests with verbose output
go test ./... -v

# Run tests with coverage
go test -v -coverprofile=coverage.out ./...
go tool cover -html=coverage.out -o coverage.html

# Run specific package tests
go test ./internal/snmp -v

# Run specific test function
go test ./internal/snmp -v -run TestTrapMessageCreation

# Run tests with race detection
go test -race ./...
```

### Test Patterns

**Table-Driven Tests**: All tests follow the table-driven pattern for consistency:

```go
func TestFunction(t *testing.T) {
    tests := []struct {
        name        string
        input       string
        expected    string
        expectError bool
    }{
        // test cases
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // test implementation
        })
    }
}
```

**SNMP-Specific Testing**: When testing SNMP functionality:

```go
// Example: Testing trap message creation
func TestTrapMessageCreation(t *testing.T) {
    trap := &TrapMessage{
        SourceIP:  "***********",
        Community: "public",
        Version:   "2c",
        OID:       "*******.*******.5.1",
        Variables: []TrapVariable{},
    }
    // Validation logic
}
```

### Adding New Tests

1. **Unit Tests**: Place in `*_test.go` files alongside source code
2. **Integration Tests**: Consider creating `tests/` directory for complex scenarios
3. **Test Naming**: Use descriptive names that explain the scenario being tested
4. **Error Testing**: Always test both success and failure cases
5. **SNMP Testing**: Use known OIDs from standard MIBs for predictable results

### CI Test Execution

Tests are automatically executed in GitLab CI during the `validation` stage:

```yaml
script:
  - go test -v -coverprofile=coverage.out ./...
  - go tool cover -html=coverage.out -o coverage.html
```

## Development Standards & Practices

### Code Organization

**Package Structure**:

```text
argus/
├── cmd/argus/           # Main application entry point
├── internal/
│   ├── config/          # Configuration handling
│   ├── snmp/            # SNMP trap handling
│   ├── translator/      # OID translation logic
│   ├── formatter/       # Message formatting using Cue templates
│   └── output/          # Output handlers (STDOUT, Elasticsearch)
├── templates/           # Cue template definitions
├── configs/             # Example configurations
├── docs/                # Documentation
└── tests/               # Test files
```

**Key Architectural Patterns**:

- **Interface-based design**: `TrapHandler` interface for pluggable trap processing
- **Pipeline pattern**: Trap → Parser → Formatter → Output
- **Dependency injection**: Configuration passed to constructors
- **Graceful shutdown**: Context-based cancellation throughout

### Linting & Code Quality

**golangci-lint Configuration**: The project uses strict linting rules defined in `.golangci.yml`:

**Critical Rules**:

- `depguard`: Enforces use of `log/slog` instead of standard `log` package
- `sloglint`: Validates structured logging patterns
- `errorlint`: Ensures proper error handling
- `revive`: Code style and best practices

**Recent Fixes Applied**:

- Migrated from `log` to `log/slog` in `internal/security/security.go` and `tools/test_trap.go`
- Updated log statements to use structured logging: `slog.Error("message", "key", value)`

### SNMP-Specific Debugging

**OID Translation Debugging**:

```go
// Enable debug logging to see OID translation
log.Debug().
    Str("oid", oid).
    Str("name", translatedName).
    Msg("Translated OID to name")
```

**Trap Processing Pipeline**:

1. **Listener** (`internal/snmp/listener.go`) - Receives UDP packets
2. **Parser** (`internal/snmp/parser.go`) - Parses SNMP packets and translates OIDs
3. **Formatter** (`internal/formatter/`) - Applies Cue templates
4. **Output** - Sends to stdout, Elasticsearch, or other configured outputs

**Common Debugging Commands**:

```bash
# Run with debug logging
./argus --debug --port=1162

# Test with snmptrap utility
snmptrap -v 2c -c public localhost:1162 '' *******.*******.5.1 \
  *******.*******.0 t 123456 \
  *******.*******.0 s "Test System Description"
```

### Error Handling Patterns

**Validation**: Use the `internal/errors` package for input validation:

```go
if err := errors.ValidateNotEmpty(configFile, "configFile"); err != nil {
    return nil, err
}
```

**Error Wrapping**: Use the custom error wrapping for context:

```go
return errors.Wrap(err, errors.ConfigError, "Failed to load configuration")
```

### Performance Considerations

**Batch Processing**: The application supports batch processing of traps for high-throughput scenarios
**Memory Management**: Use structured logging to avoid string concatenation overhead
**Goroutine Management**: Proper context cancellation for graceful shutdown

### CI/CD Integration

**Pipeline Stages**:

1. **lint**: golangci-lint with code quality reports
2. **security**: gosec security analysis
3. **validate**: Tests, formatting, and coverage
4. **deploy**: Container image build with Kaniko

**Artifacts Generated**:

- `golangci-lint-report.json` - Code quality report
- `gosec-report.json` - Security analysis
- `coverage.out` / `coverage.html` - Test coverage reports

## MIB Management & OID Translation

### MIB File Handling

**MIB Search Paths**: Configure multiple MIB directories in order of precedence:

```yaml
snmp:
  mib_paths: ["/usr/share/snmp/mibs", "./mibs", "/opt/mibs"]
```

**Supported MIB Formats**:

- Standard ASN.1 MIB files (`.mib`, `.txt`)
- Compiled MIB databases
- Custom enterprise MIBs

**OID Translation Strategy**:

1. **Exact Match**: Direct OID-to-name mapping from MIB files
2. **Prefix Match**: Partial OID matching with suffix preservation
3. **Fallback**: Return original OID if no translation found

### Custom MIB Integration

**Adding Enterprise MIBs**:

1. Place MIB files in configured `mib_paths`
2. Ensure proper MIB dependencies are available
3. Test OID translation with debug logging enabled

**MIB Validation**:

```bash
# Test OID translation
./argus --debug --config=test-config.yml
# Send test trap with enterprise OID
snmptrap -v 2c -c public localhost:1162 '' *******.4.1.12345.1.1 ...
```

## Template System & Message Formatting

### Cue Template Engine

**Template Location**: `./templates/` directory (configurable)
**Default Template**: `default.cue`

**Template Structure**:

```cue
// Example template for trap formatting
{
    timestamp: string
    source_ip: string
    trap_oid: string
    variables: [...{
        oid: string
        name: string
        type: string
        value: _
    }]
}
```

**Custom Templates**: Create templates for specific trap types or sources:

```yaml
templates:
  path: "./templates"
  default: "default.cue"
  # Map specific OIDs to custom templates
  mappings:
    "*******.4.1.12345.*": "enterprise.cue"
```

## Security Considerations

### Network Security

**Port Binding**: Default SNMP trap port 162 requires root privileges
**Recommended**: Use non-privileged port (1162) with port forwarding:

```bash
# iptables rule for port forwarding
iptables -t nat -A PREROUTING -p udp --dport 162 -j REDIRECT --to-port 1162
```

**Community Strings**: Configure appropriate SNMP community strings:

```yaml
snmp:
  community: "monitoring"  # Avoid default "public"
```

### Input Validation

**Packet Validation**: All incoming SNMP packets are validated before processing
**OID Sanitization**: OIDs are validated and sanitized during translation
**Rate Limiting**: Consider implementing rate limiting for high-traffic environments

## Troubleshooting Guide

### Common Issues

#### 1. No Trap Output Visible

- **Cause**: Batch processing configuration
- **Solution**: Set `batch_size: 1` in configuration for immediate processing
- **Debug**: Enable debug logging to see trap reception

#### 2. OID Translation Failures

- **Cause**: Missing or incorrect MIB files
- **Solution**: Verify MIB paths and file permissions
- **Debug**: Check debug logs for MIB loading messages

#### 3. Permission Denied on Port 162

- **Cause**: Non-root user cannot bind to privileged ports
- **Solution**: Use port 1162 or run with appropriate privileges
- **Alternative**: Use systemd socket activation

#### 4. High Memory Usage*

- **Cause**: Large batch sizes or memory leaks
- **Solution**: Tune batch processing parameters
- **Monitor**: Use health check endpoints for monitoring

### Debug Commands

```bash
# Enable all debug logging
./argus --debug --config=config.yml

# Test configuration loading
./argus --config=config.yml --version

# Validate MIB loading
./argus --debug --mib-paths="/path/to/mibs" --port=1162

# Test with minimal configuration
./argus --port=1162 --format=json --output=stdout
```

### Health Monitoring

**Health Check Endpoint**: `http://localhost:8080/health` (configurable)
**Metrics Available**:

- Application status
- SNMP listener status
- Configuration validation
- Component health status

**Example Health Response**:

```json
{
  "status": "up",
  "components": {
    "snmp": {
      "status": "up",
      "details": {"port": 1162}
    }
  }
}
```
