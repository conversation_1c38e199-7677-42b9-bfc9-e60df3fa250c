stages:
  - lint
  - validate
  - security
  - deploy

variables:
  PROJECT_NAME: argus
  GO_VERSION: 1.24.3
  ALPINE_VERSION: 3.21
  GOSEC_IMAGE_TAG: 2.22.4
  GOLANGCI_LINT_IMAGE_TAG: v2.1.6-alpine
  IMAGE_TAG_LATEST: latest
  GOLANG_IMAGE: $DOCKER_HUB_URL/library/golang:$GO_VERSION-alpine
  CONTAINER_IMAGE: $REGISTRY_HARBOR/$PROJECT_NAME/$PROJECT_NAME
  GOCACHE: /tmp/go-cache

# ------------------------
# Code Linting Stage
# ------------------------
lint:
  stage: lint
  image: $DOCKER_HUB_URL/golangci/golangci-lint:$GOLANGCI_LINT_IMAGE_TAG
  rules:
    - changes:
        - "**/*.go"
      when: always
    - if: $CI_COMMIT_TAG
      when: always
  script:
    - golangci-lint run --output.code-climate.path golangci-lint-report.json
  artifacts:
    expire_in: 3 days
    paths:
      - golangci-lint-report.json
    reports:
      codequality: golangci-lint-report.json

# ------------------------
# Security Analysis Stage
# ------------------------
security:
  stage: security
  image: $DOCKER_HUB_URL/securego/gosec:$GOSEC_IMAGE_TAG
  rules:
    - changes:
        - "**/*.go"
        - "go.mod"
        - "go.sum"
      when: always
    - if: $CI_COMMIT_TAG
      when: always
  script:
    - gosec -fmt=json -out=gosec-report.json ./...
  artifacts:
    expire_in: 3 days
    paths:
      - gosec-report.json
    reports:
      sast: gosec-report.json

# ------------------------
# Extended Static Analysis Stage
# ------------------------
static-analysis:
  stage: security
  image: $GOLANG_IMAGE
  rules:
    - changes:
        - "**/*.go"
        - "go.mod"
        - "go.sum"
        - "Makefile"
        - "scripts/static-analysis.sh"
      when: always
    - if: $CI_COMMIT_TAG
      when: always
  cache:
    key: ${CI_COMMIT_REF_SLUG}-tools
    paths:
      - /go/pkg/mod
      - /tmp/go-cache
      - /go/bin
  before_script:
    - export PATH=$PATH:$(go env GOPATH)/bin
    - mkdir -p /usr/local/share/ca-certificates
    - echo "$PKI_ROOT_CA" > /usr/local/share/ca-certificates/root-ca.crt
    - echo "$PKI_WEB_01_CA" > /usr/local/share/ca-certificates/prosdis-web-ca.crt
    - export GOPROXY="$GOPROXY_ADDR"
    - apk add --no-cache ca-certificates git curl make
    - update-ca-certificates
    - go mod download
  script:
    - make install-tools
    - make ci-analysis
  artifacts:
    expire_in: 7 days
    paths:
      - reports/
    reports:
      junit: reports/*junit*.xml
  allow_failure: true

# ------------------------
# Code Validation Stage
# ------------------------
validation:
  stage: validate
  image: $GOLANG_IMAGE
  rules:
    - changes:
        - "**/*.go"
        - "go.mod"
        - "go.sum"
      when: always
    - if: $CI_COMMIT_TAG
      when: always
  cache:
    key: ${CI_COMMIT_REF_SLUG}
    paths:
      - /go/pkg/mod
      - /tmp/go-cache
      - go.sum
      - go.mod
  script:
    - export PATH=$PATH:$(go env GOPATH)/bin
    - mkdir -p /usr/local/share/ca-certificates
    - echo "$PKI_ROOT_CA" > /usr/local/share/ca-certificates/root-ca.crt
    - echo "$PKI_WEB_01_CA" > /usr/local/share/ca-certificates/prosdis-web-ca.crt
    - export GOPROXY="$GOPROXY_ADDR"
    - apk add --no-cache ca-certificates git curl
    - update-ca-certificates
    - go mod tidy
    - go fmt ./...
    - go vet ./...
    - go test -v -coverprofile=coverage.out ./...
    - go tool cover -html=coverage.out -o coverage.html
  artifacts:
    when: always
    expire_in: 3 days
    paths:
      - coverage.out
      - coverage.html

# ------------------------
# Deployment Stage
# ------------------------
deploy:
  stage: deploy
  image:
    name: gcr.io/kaniko-project/executor:debug
    entrypoint: [""]
  needs:
    - lint
    - security
    - validation
  rules:
    - if: $CI_COMMIT_TAG
  before_script:
    - mkdir -p /kaniko/.docker
    - echo "{\"auths\":{\"$REGISTRY_HARBOR\":{\"auth\":\"$(printf "%s:%s" "$REGISTRY_HARBOR_USER" "$REGISTRY_HARBOR_PASSWORD" | base64 | tr -d '\n')\"}}}" > /kaniko/.docker/config.json
    - echo -e "$PKI_ROOT_CA\n$PKI_WEB_01_CA" | tee -a /kaniko/ssl/certs/ca-certificates.crt > ${CI_PROJECT_DIR}/certificates.crt
    - |
      if [ -n "$CI_COMMIT_TAG" ]; then
        export IMAGE_TAG=$CI_COMMIT_TAG
      else
        export IMAGE_TAG=$(date '+%y%m%d-%H%M')
      fi
    - |
      cat > ${CI_PROJECT_DIR}/Dockerfile.dynamic << 'EOF'
      FROM golang:${GO_VERSION}-alpine AS builder
      ARG PROJECT_NAME
      ENV GOPROXY=${GOPROXY_ADDR:-"https://proxy.golang.org"} GO111MODULE=on CGO_ENABLED=0
      COPY certificates.crt /usr/local/share/ca-certificates/certificates.crt
      RUN apk add --no-cache make git upx ca-certificates && update-ca-certificates
      WORKDIR /go/src
      COPY . .
      RUN mkdir -p build && go mod tidy && go build -o build/app .
      RUN upx --best build/app
      FROM alpine:${ALPINE_VERSION}
      ARG PROJECT_NAME
      COPY --from=builder /go/src/build/app /usr/local/bin/${PROJECT_NAME}
      RUN addgroup -S ${PROJECT_NAME} && adduser -S -G ${PROJECT_NAME} ${PROJECT_NAME}
      RUN chown ${PROJECT_NAME}:${PROJECT_NAME} /usr/local/bin/${PROJECT_NAME}
      USER ${PROJECT_NAME}
      ENTRYPOINT ["/usr/local/bin/${PROJECT_NAME}"]
      CMD ["--help"]
      EOF
  script:
    - |
      /kaniko/executor \
        --context="${CI_PROJECT_DIR}" \
        --dockerfile="${CI_PROJECT_DIR}/Dockerfile.dynamic" \
        --destination="${CONTAINER_IMAGE}:${IMAGE_TAG}" \
        --destination="${CONTAINER_IMAGE}:${IMAGE_TAG_LATEST}" \
        --build-arg PROJECT_NAME=${PROJECT_NAME} \
        --build-arg GO_VERSION=${GO_VERSION}
