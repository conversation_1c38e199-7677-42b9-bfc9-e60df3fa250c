# Argus SNMP Trap Receiver - Static Analysis & Build Automation
# =============================================================

# Project configuration
PROJECT_NAME := argus
GO_VERSION := 1.24.3
BUILD_DIR := build
COVERAGE_DIR := coverage
REPORTS_DIR := reports

# Go build flags
LDFLAGS := -s -w
BUILD_FLAGS := -ldflags "$(LDFLAGS)"

# Static analysis tools versions
GOVULNCHECK_VERSION := latest
STATICCHECK_VERSION := latest
INEFFASSIGN_VERSION := latest
GOCYCLO_VERSION := latest
DUPL_VERSION := latest

# Default target
.PHONY: all
all: clean lint test build

# =============================================================
# Build Targets
# =============================================================

.PHONY: build
build: ## Build the application
	@echo "Building $(PROJECT_NAME)..."
	@mkdir -p $(BUILD_DIR)
	go build $(BUILD_FLAGS) -o $(BUILD_DIR)/$(PROJECT_NAME) .

.PHONY: build-release
build-release: ## Build optimized release binary
	@echo "Building release version of $(PROJECT_NAME)..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 go build $(BUILD_FLAGS) -a -installsuffix cgo -o $(BUILD_DIR)/$(PROJECT_NAME) .

.PHONY: clean
clean: ## Clean build artifacts and reports
	@echo "Cleaning build artifacts..."
	@rm -rf $(BUILD_DIR) $(COVERAGE_DIR) $(REPORTS_DIR)
	@go clean -cache -testcache -modcache

# =============================================================
# Testing Targets
# =============================================================

.PHONY: test
test: ## Run all tests
	@echo "Running tests..."
	@mkdir -p $(COVERAGE_DIR)
	go test -v -race -coverprofile=$(COVERAGE_DIR)/coverage.out ./...

.PHONY: test-coverage
test-coverage: test ## Generate test coverage report
	@echo "Generating coverage report..."
	@mkdir -p $(COVERAGE_DIR)
	go tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(COVERAGE_DIR)/coverage.html
	go tool cover -func=$(COVERAGE_DIR)/coverage.out | tail -1

.PHONY: test-integration
test-integration: ## Run integration tests (if any)
	@echo "Running integration tests..."
	go test -v -tags=integration ./tests/...

# =============================================================
# Static Analysis Targets
# =============================================================

.PHONY: lint
lint: golangci-lint ## Run golangci-lint

.PHONY: golangci-lint
golangci-lint: ## Run golangci-lint
	@echo "Running golangci-lint..."
	@mkdir -p $(REPORTS_DIR)
	golangci-lint run --out-format=json > $(REPORTS_DIR)/golangci-lint.json || true
	golangci-lint run

.PHONY: security
security: gosec govulncheck ## Run security analysis

.PHONY: gosec
gosec: ## Run gosec security scanner
	@echo "Running gosec security analysis..."
	@mkdir -p $(REPORTS_DIR)
	gosec -fmt=json -out=$(REPORTS_DIR)/gosec.json ./... || true
	gosec ./...

.PHONY: govulncheck
govulncheck: install-govulncheck ## Run Go vulnerability scanner
	@echo "Running govulncheck..."
	@mkdir -p $(REPORTS_DIR)
	govulncheck -json ./... > $(REPORTS_DIR)/govulncheck.json || true
	govulncheck ./...

.PHONY: staticcheck
staticcheck: install-staticcheck ## Run staticcheck analysis
	@echo "Running staticcheck..."
	@mkdir -p $(REPORTS_DIR)
	staticcheck -f=json ./... > $(REPORTS_DIR)/staticcheck.json || true
	staticcheck ./...

.PHONY: ineffassign
ineffassign: install-ineffassign ## Check for ineffectual assignments
	@echo "Running ineffassign..."
	@mkdir -p $(REPORTS_DIR)
	ineffassign ./... > $(REPORTS_DIR)/ineffassign.txt || true
	ineffassign ./...

.PHONY: gocyclo
gocyclo: install-gocyclo ## Check cyclomatic complexity
	@echo "Running gocyclo (complexity > 10)..."
	@mkdir -p $(REPORTS_DIR)
	gocyclo -over 10 . > $(REPORTS_DIR)/gocyclo.txt || true
	gocyclo -over 10 .

.PHONY: dupl
dupl: install-dupl ## Check for code duplication
	@echo "Running dupl (threshold: 50)..."
	@mkdir -p $(REPORTS_DIR)
	dupl -threshold 50 . > $(REPORTS_DIR)/dupl.txt || true
	dupl -threshold 50 .

.PHONY: analysis
analysis: lint security staticcheck ineffassign gocyclo dupl ## Run comprehensive static analysis
	@echo "Static analysis complete. Reports available in $(REPORTS_DIR)/"

# =============================================================
# Tool Installation Targets
# =============================================================

.PHONY: install-tools
install-tools: install-govulncheck install-staticcheck install-ineffassign install-gocyclo install-dupl ## Install all static analysis tools

.PHONY: install-govulncheck
install-govulncheck:
	@echo "Installing govulncheck..."
	@go install golang.org/x/vuln/cmd/govulncheck@$(GOVULNCHECK_VERSION)

.PHONY: install-staticcheck
install-staticcheck:
	@echo "Installing staticcheck..."
	@go install honnef.co/go/tools/cmd/staticcheck@$(STATICCHECK_VERSION)

.PHONY: install-ineffassign
install-ineffassign:
	@echo "Installing ineffassign..."
	@go install github.com/gordonklaus/ineffassign@$(INEFFASSIGN_VERSION)

.PHONY: install-gocyclo
install-gocyclo:
	@echo "Installing gocyclo..."
	@go install github.com/fzipp/gocyclo/cmd/gocyclo@$(GOCYCLO_VERSION)

.PHONY: install-dupl
install-dupl:
	@echo "Installing dupl..."
	@go install github.com/mibk/dupl@$(DUPL_VERSION)

# =============================================================
# Development Targets
# =============================================================

.PHONY: fmt
fmt: ## Format code
	@echo "Formatting code..."
	go fmt ./...
	goimports -w .

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	go vet ./...

.PHONY: mod-tidy
mod-tidy: ## Tidy go modules
	@echo "Tidying go modules..."
	go mod tidy
	go mod verify

.PHONY: deps
deps: ## Download dependencies
	@echo "Downloading dependencies..."
	go mod download

# =============================================================
# CI/CD Integration Targets
# =============================================================

.PHONY: ci-lint
ci-lint: ## CI linting (with JSON output)
	@mkdir -p $(REPORTS_DIR)
	golangci-lint run --out-format=json > $(REPORTS_DIR)/golangci-lint.json

.PHONY: ci-security
ci-security: ## CI security analysis (with JSON output)
	@mkdir -p $(REPORTS_DIR)
	gosec -fmt=json -out=$(REPORTS_DIR)/gosec.json ./...
	govulncheck -json ./... > $(REPORTS_DIR)/govulncheck.json

.PHONY: ci-analysis
ci-analysis: install-tools ci-lint ci-security ## Full CI static analysis
	@mkdir -p $(REPORTS_DIR)
	staticcheck -f=json ./... > $(REPORTS_DIR)/staticcheck.json || true
	ineffassign ./... > $(REPORTS_DIR)/ineffassign.txt || true
	gocyclo -over 10 . > $(REPORTS_DIR)/gocyclo.txt || true
	dupl -threshold 50 . > $(REPORTS_DIR)/dupl.txt || true

# =============================================================
# Help Target
# =============================================================

.PHONY: help
help: ## Show this help message
	@echo "Argus SNMP Trap Receiver - Build & Analysis Commands"
	@echo "=================================================="
	@echo ""
	@echo "Build Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(build|clean)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'
	@echo ""
	@echo "Test Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "test" | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'
	@echo ""
	@echo "Static Analysis Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(lint|security|analysis|staticcheck|ineffassign|gocyclo|dupl|gosec|govulncheck)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'
	@echo ""
	@echo "Development Commands:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "(fmt|vet|mod|deps)" | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'
	@echo ""
	@echo "Tool Installation:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | grep -E "install" | awk 'BEGIN {FS = ":.*?## "}; {printf "  %-20s %s\n", $$1, $$2}'
