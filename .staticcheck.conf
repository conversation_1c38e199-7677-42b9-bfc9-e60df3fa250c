# Staticcheck configuration for Argus SNMP Trap Receiver
# ======================================================

# Enable all checks by default
checks = ["all"]

# Disable specific checks that may not be relevant
# or cause false positives in this project
exclude = [
    # ST1000: Package comment should be of the form "Package x ..."
    # Disabled because we use more descriptive package comments
    "ST1000",
    
    # ST1003: API should use camelCase
    # Disabled for SNMP-specific naming conventions
    "ST1003",
    
    # ST1016: Methods on the same type should have the same receiver name
    # Disabled to allow context-specific receiver names
    "ST1016",
    
    # ST1020: Comment should be of the form "Deprecated: ..."
    # Disabled because we don't have deprecated functions yet
    "ST1020",
    
    # ST1021: Comment should be of the form "BUG(who): ..."
    # Disabled because we use issue tracking instead
    "ST1021"
]

# Set the minimum Go version for analysis
go = "1.24"

# Enable additional checks for potential issues
[checks.arguments]
# Check for unused function parameters
unused_params = true

# Check for potential nil pointer dereferences
nil_deref = true

# Check for unreachable code
unreachable = true

# Check for ineffective assignments
ineffassign = true

# SNMP-specific configuration
[snmp]
# Allow SNMP-specific naming patterns
allow_snmp_naming = true

# Allow OID-related constants and variables
allow_oid_patterns = true

# Performance-related checks
[performance]
# Check for string concatenation in loops
string_concat = true

# Check for unnecessary allocations
unnecessary_alloc = true

# Check for inefficient map operations
map_operations = true
