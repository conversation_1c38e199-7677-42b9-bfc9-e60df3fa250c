package main

import (
	"argus/internal/formatter"
	"argus/internal/health"
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/snmp"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Version information (to be set during build)
var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

func main() {
	// Parse command-line flags
	configFile := flag.String("config", "configs/config.yml", "Path to configuration file")
	port := flag.Int("port", 0, "UDP port to listen for SNMP traps (overrides config file)")
	mibPaths := flag.String("mib-paths", "", "Comma-separated list of directories containing MIB files (overrides config file)")
	outputType := flag.String("output", "", "Output type: stdout, elasticsearch, both (overrides config file)")
	outputFormat := flag.String("format", "", "Output format: text, json, logfmt (overrides config file)")
	debug := flag.Bool("debug", false, "Enable debug logging")
	showVersion := flag.Bool("version", false, "Show version information")
	flag.Parse()

	// Show version information if requested
	if *showVersion {
		fmt.Printf("Argus %s (commit: %s, built at: %s)\n", version, commit, date)
		os.Exit(0)
	}

	// Initialize logger
	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	if *debug {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else {
		zerolog.SetGlobalLevel(zerolog.InfoLevel)
	}

	log.Info().
		Str("version", version).
		Msg("Starting Argus SNMP trap receiver")

	if *debug {
		log.Debug().Msg("Debug logging enabled")
	}

	// Load configuration
	log.Info().Str("config_file", *configFile).Msg("Loading configuration")
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		errors.HandleError(err, true)
	}

	// Override configuration with command-line flags
	if err := config.UpdateConfigFromFlags(cfg, *port, *mibPaths, *outputType, *outputFormat); err != nil {
		errors.HandleError(err, true)
	}

	// Update log level from configuration if not in debug mode
	if !*debug && cfg.Logging.Level != "" {
		level, err := zerolog.ParseLevel(cfg.Logging.Level)
		if err != nil {
			log.Warn().
				Err(err).
				Str("level", cfg.Logging.Level).
				Msg("Invalid log level in configuration, using default")
		} else {
			zerolog.SetGlobalLevel(level)
			log.Info().
				Str("level", level.String()).
				Msg("Log level set from configuration")
		}
	}

	// Log effective configuration
	log.Info().
		Int("port", cfg.Server.Port).
		Strs("mib_paths", cfg.SNMP.MIBPaths).
		Bool("stdout_enabled", cfg.Output.Stdout.Enabled).
		Str("stdout_format", cfg.Output.Stdout.Format).
		Bool("elasticsearch_enabled", cfg.Output.Elasticsearch.Enabled).
		Msg("Configuration loaded")

	// Initialize SNMP trap handler
	log.Info().Msg("Initializing SNMP trap handler...")
	trapHandler, err := snmp.NewDefaultTrapHandler(cfg)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to create SNMP trap handler"), true)
	}

	// Initialize template engine
	log.Info().Msg("Initializing template engine...")
	templateEngine, err := formatter.NewTemplateEngine(cfg)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.TemplateError, "Failed to create template engine"), true)
	}
	log.Info().
		Str("template_path", cfg.Templates.Path).
		Str("default_template", cfg.Templates.Default).
		Msg("Template engine initialized")

	// Initialize output handlers
	log.Info().Msg("Initializing output handlers...")

	// Add console output
	if cfg.Output.Stdout.Enabled {
		consoleOutput, err := snmp.NewConsoleOutput(cfg.Output.Stdout.Format)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to create console output"), true)
		}

		// Wrap console output with formatter
		formatterOutput, err := formatter.NewFormatterOutput(templateEngine, consoleOutput)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to create formatter output"), true)
		}

		// Register output handler
		if err := trapHandler.RegisterOutput(formatterOutput); err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to register output handler"), true)
		}

		log.Info().
			Str("format", cfg.Output.Stdout.Format).
			Msg("Console output handler initialized with formatter")
	}

	// TODO: Initialize Elasticsearch output if enabled
	if cfg.Output.Elasticsearch.Enabled {
		log.Info().
			Str("url", cfg.Output.Elasticsearch.URL).
			Str("index", cfg.Output.Elasticsearch.Index).
			Msg("Elasticsearch output handler not yet implemented")
	}

	// Initialize SNMP trap listener
	log.Info().Msg("Initializing SNMP trap listener...")
	trapListener, err := snmp.NewTrapListener(cfg, trapHandler)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to create SNMP trap listener"), true)
	}

	// Initialize health check server if enabled
	var healthServer *health.Server
	if cfg.Health.Enabled {
		log.Info().Msg("Initializing health check server...")
		healthServer, err = health.NewServer(&cfg.Health)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.ConfigError, "Failed to create health check server"), true)
		}

		// Register SNMP trap handler as a component
		healthServer.RegisterComponent("snmp", func() health.ComponentStatus {
			return health.ComponentStatus{
				Status: health.StatusUp,
				Details: map[string]interface{}{
					"port": cfg.Server.Port,
				},
			}
		})

		log.Info().
			Int("port", cfg.Health.Port).
			Str("bind_address", cfg.Health.BindAddress).
			Msg("Health check server initialized")
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the SNMP trap listener
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := trapListener.Start(ctx); err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to start SNMP trap listener"), true)
	}

	// Start the health check server if enabled
	if cfg.Health.Enabled && healthServer != nil {
		if err := healthServer.Start(ctx); err != nil {
			errors.HandleError(errors.Wrap(err, errors.NetworkError, "Failed to start health check server"), true)
		}
	}

	log.Info().
		Int("port", cfg.Server.Port).
		Msg("Argus is running. Press Ctrl+C to stop.")

	// Wait for termination signal
	sig := <-sigChan
	log.Info().Str("signal", sig.String()).Msg("Received signal, shutting down...")

	// Perform graceful shutdown
	log.Info().Msg("Stopping SNMP trap listener...")
	if err := trapListener.Stop(); err != nil {
		// Log the error but don't exit, as we're already shutting down
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Error stopping SNMP trap listener"), false)
	}

	// Stop the health check server if enabled
	if cfg.Health.Enabled && healthServer != nil {
		log.Info().Msg("Stopping health check server...")
		if err := healthServer.Stop(); err != nil {
			// Log the error but don't exit, as we're already shutting down
			errors.HandleError(errors.Wrap(err, errors.NetworkError, "Error stopping health check server"), false)
		}
	}

	// Cancel the context to stop any remaining goroutines
	cancel()

	log.Info().Msg("Shutdown complete")
}
