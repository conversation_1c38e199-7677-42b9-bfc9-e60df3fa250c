package main

import (
	"argus/internal/formatter"
	"argus/internal/health"
	"context"
	"flag"
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/snmp"
)

// Version information (to be set during build)
var (
	version = "dev"
	commit  = "none"
	date    = "unknown"
)

func main() {
	// Parse command-line flags
	configFile := flag.String("config", "configs/config.yml", "Path to configuration file")
	port := flag.Int("port", 0, "UDP port to listen for SNMP traps (overrides config file)")
	mibPaths := flag.String("mib-paths", "", "Comma-separated list of directories containing MIB files (overrides config file)")
	outputType := flag.String("output", "", "Output type: stdout, elasticsearch, both (overrides config file)")
	outputFormat := flag.String("format", "", "Output format: text, json, logfmt (overrides config file)")
	debug := flag.Bool("debug", false, "Enable debug logging")
	showVersion := flag.Bool("version", false, "Show version information")
	flag.Parse()

	// Show version information if requested
	if *showVersion {
		fmt.Printf("Argus %s (commit: %s, built at: %s)\n", version, commit, date)
		os.Exit(0)
	}

	// Initialize logger
	var logLevel slog.Level
	if *debug {
		logLevel = slog.LevelDebug
	} else {
		logLevel = slog.LevelInfo
	}

	// Create a text handler with custom options
	opts := &slog.HandlerOptions{
		Level: logLevel,
	}
	handler := slog.NewTextHandler(os.Stdout, opts)
	logger := slog.New(handler)
	slog.SetDefault(logger)

	slog.Info("Starting Argus SNMP trap receiver", "version", version)

	if *debug {
		slog.Debug("Debug logging enabled")
	}

	// Load configuration
	slog.Info("Loading configuration", "config_file", *configFile)
	cfg, err := config.LoadConfig(*configFile)
	if err != nil {
		errors.HandleError(err, true)
	}

	// Override configuration with command-line flags
	if err := config.UpdateConfigFromFlags(cfg, *port, *mibPaths, *outputType, *outputFormat); err != nil {
		errors.HandleError(err, true)
	}

	// Update log level from configuration if not in debug mode
	if !*debug && cfg.Logging.Level != "" {
		var level slog.Level
		switch cfg.Logging.Level {
		case "debug":
			level = slog.LevelDebug
		case "info":
			level = slog.LevelInfo
		case "warn":
			level = slog.LevelWarn
		case "error":
			level = slog.LevelError
		default:
			slog.Warn("Invalid log level in configuration, using default",
				"level", cfg.Logging.Level)
			level = slog.LevelInfo
		}

		// Update the logger with new level
		opts := &slog.HandlerOptions{
			Level: level,
		}
		handler := slog.NewTextHandler(os.Stdout, opts)
		logger := slog.New(handler)
		slog.SetDefault(logger)

		slog.Info("Log level set from configuration", "level", cfg.Logging.Level)
	}

	// Log effective configuration
	slog.Info("Configuration loaded",
		"port", cfg.Server.Port,
		"mib_paths", cfg.SNMP.MIBPaths,
		"stdout_enabled", cfg.Output.Stdout.Enabled,
		"stdout_format", cfg.Output.Stdout.Format,
		"elasticsearch_enabled", cfg.Output.Elasticsearch.Enabled)

	// Initialize SNMP trap handler
	slog.Info("Initializing SNMP trap handler...")
	trapHandler, err := snmp.NewDefaultTrapHandler(cfg)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to create SNMP trap handler"), true)
	}

	// Initialize template engine
	slog.Info("Initializing template engine...")
	templateEngine, err := formatter.NewTemplateEngine(cfg)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.TemplateError, "Failed to create template engine"), true)
	}
	slog.Info("Template engine initialized",
		"template_path", cfg.Templates.Path,
		"default_template", cfg.Templates.Default)

	// Initialize output handlers
	slog.Info("Initializing output handlers...")

	// Add console output
	if cfg.Output.Stdout.Enabled {
		consoleOutput, err := snmp.NewConsoleOutput(cfg.Output.Stdout.Format)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to create console output"), true)
		}

		// Wrap console output with formatter
		formatterOutput, err := formatter.NewFormatterOutput(templateEngine, consoleOutput)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to create formatter output"), true)
		}

		// Register output handler
		if err := trapHandler.RegisterOutput(formatterOutput); err != nil {
			errors.HandleError(errors.Wrap(err, errors.OutputError, "Failed to register output handler"), true)
		}

		slog.Info("Console output handler initialized with formatter",
			"format", cfg.Output.Stdout.Format)
	}

	// TODO: Initialize Elasticsearch output if enabled
	if cfg.Output.Elasticsearch.Enabled {
		slog.Info("Elasticsearch output handler not yet implemented",
			"url", cfg.Output.Elasticsearch.URL,
			"index", cfg.Output.Elasticsearch.Index)
	}

	// Initialize SNMP trap listener
	slog.Info("Initializing SNMP trap listener...")
	trapListener, err := snmp.NewTrapListener(cfg, trapHandler)
	if err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to create SNMP trap listener"), true)
	}

	// Initialize health check server if enabled
	var healthServer *health.Server
	if cfg.Health.Enabled {
		slog.Info("Initializing health check server...")
		healthServer, err = health.NewServer(&cfg.Health)
		if err != nil {
			errors.HandleError(errors.Wrap(err, errors.ConfigError, "Failed to create health check server"), true)
		}

		// Register SNMP trap handler as a component
		healthServer.RegisterComponent("snmp", func() health.ComponentStatus {
			return health.ComponentStatus{
				Status: health.StatusUp,
				Details: map[string]interface{}{
					"port": cfg.Server.Port,
				},
			}
		})

		slog.Info("Health check server initialized",
			"port", cfg.Health.Port,
			"bind_address", cfg.Health.BindAddress)
	}

	// Set up signal handling for graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start the SNMP trap listener
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	if err := trapListener.Start(ctx); err != nil {
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Failed to start SNMP trap listener"), true)
	}

	// Start the health check server if enabled
	if cfg.Health.Enabled && healthServer != nil {
		if err := healthServer.Start(ctx); err != nil {
			errors.HandleError(errors.Wrap(err, errors.NetworkError, "Failed to start health check server"), true)
		}
	}

	slog.Info("Argus is running. Press Ctrl+C to stop.",
		"port", cfg.Server.Port)

	// Wait for termination signal
	sig := <-sigChan
	slog.Info("Received signal, shutting down...", "signal", sig.String())

	// Perform graceful shutdown
	slog.Info("Stopping SNMP trap listener...")
	if err := trapListener.Stop(); err != nil {
		// Log the error but don't exit, as we're already shutting down
		errors.HandleError(errors.Wrap(err, errors.SNMPError, "Error stopping SNMP trap listener"), false)
	}

	// Stop the health check server if enabled
	if cfg.Health.Enabled && healthServer != nil {
		slog.Info("Stopping health check server...")
		if err := healthServer.Stop(); err != nil {
			// Log the error but don't exit, as we're already shutting down
			errors.HandleError(errors.Wrap(err, errors.NetworkError, "Error stopping health check server"), false)
		}
	}

	// Cancel the context to stop any remaining goroutines
	cancel()

	slog.Info("Shutdown complete")
}
