package tests

import (
	"os"
	"os/exec"
	"path/filepath"
	"testing"
)

// TestStaticAnalysisToolsAvailable verifies that all static analysis tools are available
func TestStaticAnalysisToolsAvailable(t *testing.T) {
	tools := []string{
		"govulncheck",
		"staticcheck",
		"ineffassign",
		"gocyclo",
		"dupl",
	}

	for _, tool := range tools {
		t.Run(tool, func(t *testing.T) {
			_, err := exec.LookPath(tool)
			if err != nil {
				t.Errorf("Tool %s not found in PATH: %v", tool, err)
			}
		})
	}
}

// TestMakefileTargets verifies that Makefile targets work correctly
func TestMakefileTargets(t *testing.T) {
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Change to project root (parent of tests directory)
	projectRoot := filepath.Dir(cwd)
	err = os.Chdir(projectRoot)
	if err != nil {
		t.Fatalf("Failed to change to project root: %v", err)
	}

	// Restore original directory at the end
	defer func() {
		os.Chdir(cwd)
	}()

	targets := []string{
		"help",
		"clean",
		"deps",
		"fmt",
		"vet",
	}

	for _, target := range targets {
		t.Run(target, func(t *testing.T) {
			cmd := exec.Command("make", target)
			output, err := cmd.CombinedOutput()
			if err != nil {
				t.Errorf("Make target %s failed: %v\nOutput: %s", target, err, output)
			}
		})
	}
}

// TestReportsDirectoryCreation verifies that reports directory is created
func TestReportsDirectoryCreation(t *testing.T) {
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Change to project root (parent of tests directory)
	projectRoot := filepath.Dir(cwd)
	err = os.Chdir(projectRoot)
	if err != nil {
		t.Fatalf("Failed to change to project root: %v", err)
	}

	// Restore original directory at the end
	defer func() {
		os.Chdir(cwd)
	}()

	// Run a simple analysis command that creates reports directory
	cmd := exec.Command("make", "install-tools")
	_, err = cmd.CombinedOutput()
	if err != nil {
		t.Fatalf("Failed to install tools: %v", err)
	}

	// Check if reports directory exists or can be created
	reportsDir := "reports"
	err = os.MkdirAll(reportsDir, 0755)
	if err != nil {
		t.Errorf("Failed to create reports directory: %v", err)
	}

	// Verify directory exists
	if _, err := os.Stat(reportsDir); os.IsNotExist(err) {
		t.Errorf("Reports directory does not exist after creation")
	}
}

// TestStaticAnalysisScript verifies that the static analysis script exists and is executable
func TestStaticAnalysisScript(t *testing.T) {
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Change to project root (parent of tests directory)
	projectRoot := filepath.Dir(cwd)
	err = os.Chdir(projectRoot)
	if err != nil {
		t.Fatalf("Failed to change to project root: %v", err)
	}

	// Restore original directory at the end
	defer func() {
		os.Chdir(cwd)
	}()

	scriptPath := filepath.Join("scripts", "static-analysis.sh")

	// Check if script exists
	if _, err := os.Stat(scriptPath); os.IsNotExist(err) {
		t.Errorf("Static analysis script does not exist: %s", scriptPath)
		return
	}

	// Check if script is executable
	info, err := os.Stat(scriptPath)
	if err != nil {
		t.Errorf("Failed to get script info: %v", err)
		return
	}

	mode := info.Mode()
	if mode&0111 == 0 {
		t.Errorf("Static analysis script is not executable: %s", scriptPath)
	}

	// Test script help
	cmd := exec.Command("bash", scriptPath, "--help")
	output, err := cmd.CombinedOutput()
	if err != nil {
		t.Errorf("Failed to run script help: %v\nOutput: %s", err, output)
	}

	// Verify help output contains expected content
	helpOutput := string(output)
	expectedStrings := []string{
		"Usage:",
		"golangci-lint",
		"gosec",
		"govulncheck",
		"staticcheck",
	}

	for _, expected := range expectedStrings {
		if !contains(helpOutput, expected) {
			t.Errorf("Help output missing expected string: %s", expected)
		}
	}
}

// TestConfigurationFiles verifies that static analysis configuration files exist
func TestConfigurationFiles(t *testing.T) {
	// Get current working directory
	cwd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current directory: %v", err)
	}

	// Change to project root (parent of tests directory)
	projectRoot := filepath.Dir(cwd)
	err = os.Chdir(projectRoot)
	if err != nil {
		t.Fatalf("Failed to change to project root: %v", err)
	}

	// Restore original directory at the end
	defer func() {
		os.Chdir(cwd)
	}()

	configFiles := []string{
		".golangci.yml",
		".staticcheck.conf",
		"Makefile",
	}

	for _, configFile := range configFiles {
		t.Run(configFile, func(t *testing.T) {
			if _, err := os.Stat(configFile); os.IsNotExist(err) {
				t.Errorf("Configuration file does not exist: %s", configFile)
			}
		})
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr ||
			s[len(s)-len(substr):] == substr ||
			containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
