#!/bin/bash
# Argus SNMP Trap Receiver - Comprehensive Static Analysis Script
# ================================================================

set -euo pipefail

# Configuration
PROJECT_NAME="argus"
REPORTS_DIR="reports"
COVERAGE_DIR="coverage"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_PREFIX="${PROJECT_NAME}_analysis_${TIMESTAMP}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install Go tools if not present
install_tools() {
    log_info "Checking and installing static analysis tools..."
    
    local tools=(
        "golang.org/x/vuln/cmd/govulncheck@latest"
        "honnef.co/go/tools/cmd/staticcheck@latest"
        "github.com/gordonklaus/ineffassign@latest"
        "github.com/fzipp/gocyclo/cmd/gocyclo@latest"
        "github.com/mibk/dupl@latest"
    )
    
    for tool in "${tools[@]}"; do
        tool_name=$(basename "${tool%@*}")
        if ! command_exists "$tool_name"; then
            log_info "Installing $tool_name..."
            go install "$tool"
        else
            log_success "$tool_name is already installed"
        fi
    done
}

# Create reports directory
setup_directories() {
    log_info "Setting up directories..."
    mkdir -p "$REPORTS_DIR" "$COVERAGE_DIR"
}

# Run golangci-lint
run_golangci_lint() {
    log_info "Running golangci-lint..."
    
    if ! command_exists golangci-lint; then
        log_warning "golangci-lint not found. Please install it first."
        return 1
    fi
    
    local report_file="${REPORTS_DIR}/golangci-lint_${TIMESTAMP}.json"
    local exit_code=0
    
    golangci-lint run --out-format=json > "$report_file" || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "golangci-lint passed"
    else
        log_warning "golangci-lint found issues (exit code: $exit_code)"
        golangci-lint run || true  # Show human-readable output
    fi
    
    return $exit_code
}

# Run gosec security analysis
run_gosec() {
    log_info "Running gosec security analysis..."
    
    if ! command_exists gosec; then
        log_warning "gosec not found. Please install it first."
        return 1
    fi
    
    local report_file="${REPORTS_DIR}/gosec_${TIMESTAMP}.json"
    local exit_code=0
    
    gosec -fmt=json -out="$report_file" ./... || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "gosec security analysis passed"
    else
        log_warning "gosec found security issues (exit code: $exit_code)"
        gosec ./... || true  # Show human-readable output
    fi
    
    return $exit_code
}

# Run govulncheck
run_govulncheck() {
    log_info "Running govulncheck vulnerability scanner..."
    
    local report_file="${REPORTS_DIR}/govulncheck_${TIMESTAMP}.json"
    local exit_code=0
    
    govulncheck -json ./... > "$report_file" || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "govulncheck found no vulnerabilities"
    else
        log_warning "govulncheck found vulnerabilities (exit code: $exit_code)"
        govulncheck ./... || true  # Show human-readable output
    fi
    
    return $exit_code
}

# Run staticcheck
run_staticcheck() {
    log_info "Running staticcheck analysis..."
    
    local report_file="${REPORTS_DIR}/staticcheck_${TIMESTAMP}.json"
    local exit_code=0
    
    staticcheck -f=json ./... > "$report_file" || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "staticcheck analysis passed"
    else
        log_warning "staticcheck found issues (exit code: $exit_code)"
        staticcheck ./... || true  # Show human-readable output
    fi
    
    return $exit_code
}

# Run ineffassign
run_ineffassign() {
    log_info "Running ineffassign analysis..."
    
    local report_file="${REPORTS_DIR}/ineffassign_${TIMESTAMP}.txt"
    local exit_code=0
    
    ineffassign ./... > "$report_file" || exit_code=$?
    
    if [ ! -s "$report_file" ]; then
        log_success "ineffassign found no issues"
    else
        log_warning "ineffassign found ineffectual assignments"
        cat "$report_file"
        exit_code=1
    fi
    
    return $exit_code
}

# Run gocyclo
run_gocyclo() {
    log_info "Running gocyclo complexity analysis..."
    
    local report_file="${REPORTS_DIR}/gocyclo_${TIMESTAMP}.txt"
    local threshold=10
    local exit_code=0
    
    gocyclo -over $threshold . > "$report_file" || exit_code=$?
    
    if [ ! -s "$report_file" ]; then
        log_success "gocyclo found no functions with complexity > $threshold"
    else
        log_warning "gocyclo found functions with high complexity (> $threshold)"
        cat "$report_file"
        exit_code=1
    fi
    
    return $exit_code
}

# Run dupl
run_dupl() {
    log_info "Running dupl code duplication analysis..."
    
    local report_file="${REPORTS_DIR}/dupl_${TIMESTAMP}.txt"
    local threshold=50
    local exit_code=0
    
    dupl -threshold $threshold . > "$report_file" || exit_code=$?
    
    if [ ! -s "$report_file" ]; then
        log_success "dupl found no code duplication (threshold: $threshold)"
    else
        log_warning "dupl found code duplication (threshold: $threshold)"
        cat "$report_file"
        exit_code=1
    fi
    
    return $exit_code
}

# Run tests with coverage
run_tests() {
    log_info "Running tests with coverage..."
    
    local coverage_file="${COVERAGE_DIR}/coverage_${TIMESTAMP}.out"
    local coverage_html="${COVERAGE_DIR}/coverage_${TIMESTAMP}.html"
    local exit_code=0
    
    go test -v -race -coverprofile="$coverage_file" ./... || exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        log_success "All tests passed"
        
        # Generate coverage report
        go tool cover -html="$coverage_file" -o "$coverage_html"
        local coverage_percent=$(go tool cover -func="$coverage_file" | tail -1 | awk '{print $3}')
        log_info "Test coverage: $coverage_percent"
        
        # Create symlinks to latest reports
        ln -sf "$(basename "$coverage_file")" "${COVERAGE_DIR}/coverage.out"
        ln -sf "$(basename "$coverage_html")" "${COVERAGE_DIR}/coverage.html"
    else
        log_error "Some tests failed (exit code: $exit_code)"
    fi
    
    return $exit_code
}

# Generate summary report
generate_summary() {
    log_info "Generating analysis summary..."
    
    local summary_file="${REPORTS_DIR}/summary_${TIMESTAMP}.txt"
    
    cat > "$summary_file" << EOF
Argus SNMP Trap Receiver - Static Analysis Summary
==================================================
Generated: $(date)
Project: $PROJECT_NAME

Analysis Results:
EOF
    
    # Check each report file and add to summary
    local tools=("golangci-lint" "gosec" "govulncheck" "staticcheck" "ineffassign" "gocyclo" "dupl")
    
    for tool in "${tools[@]}"; do
        local report_pattern="${REPORTS_DIR}/${tool}_${TIMESTAMP}.*"
        if ls $report_pattern 1> /dev/null 2>&1; then
            local report_file=$(ls $report_pattern | head -1)
            if [ -s "$report_file" ] && [[ "$tool" != "golangci-lint" && "$tool" != "gosec" && "$tool" != "govulncheck" && "$tool" != "staticcheck" ]]; then
                echo "- $tool: ISSUES FOUND" >> "$summary_file"
            else
                echo "- $tool: PASSED" >> "$summary_file"
            fi
        else
            echo "- $tool: NOT RUN" >> "$summary_file"
        fi
    done
    
    echo "" >> "$summary_file"
    echo "Report files generated in: $REPORTS_DIR/" >> "$summary_file"
    echo "Coverage reports in: $COVERAGE_DIR/" >> "$summary_file"
    
    log_success "Summary report generated: $summary_file"
    cat "$summary_file"
}

# Main execution
main() {
    log_info "Starting comprehensive static analysis for $PROJECT_NAME"
    
    setup_directories
    install_tools
    
    local overall_exit_code=0
    
    # Run all analysis tools
    run_tests || overall_exit_code=1
    run_golangci_lint || overall_exit_code=1
    run_gosec || overall_exit_code=1
    run_govulncheck || overall_exit_code=1
    run_staticcheck || overall_exit_code=1
    run_ineffassign || overall_exit_code=1
    run_gocyclo || overall_exit_code=1
    run_dupl || overall_exit_code=1
    
    generate_summary
    
    if [ $overall_exit_code -eq 0 ]; then
        log_success "All static analysis checks passed!"
    else
        log_warning "Some static analysis checks found issues. Check the reports for details."
    fi
    
    exit $overall_exit_code
}

# Parse command line arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help]"
        echo "Run comprehensive static analysis for the Argus SNMP Trap Receiver"
        echo ""
        echo "This script runs the following tools:"
        echo "  - golangci-lint (code quality)"
        echo "  - gosec (security analysis)"
        echo "  - govulncheck (vulnerability scanning)"
        echo "  - staticcheck (advanced static analysis)"
        echo "  - ineffassign (ineffectual assignments)"
        echo "  - gocyclo (cyclomatic complexity)"
        echo "  - dupl (code duplication)"
        echo "  - go test (with coverage)"
        echo ""
        echo "Reports are generated in the 'reports/' directory"
        exit 0
        ;;
    *)
        main "$@"
        ;;
esac
