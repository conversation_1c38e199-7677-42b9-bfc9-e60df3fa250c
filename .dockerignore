# Go build cache
*.log
*.out
*.test
*.prof
*.bench
*.coverprofile

# Dependency directories
vendor/
bin/
build/
dist/

# Executables
*.exe
*.dll
*.so
*.dylib
*.a
*.o

# Local dev files
.env
.env.*
*.local
*.swp
*.bak
*.tmp
*.old

# IDE/editor files
.vscode/
.idea/

# Git-related
.git
.gitignore
.gitattributes

# OS-specific
.DS_Store
Thumbs.db

# MIBs, templates, configs, docs, and test data (ignore generated or local data, not source)
mibs/
templates/
configs/
docs/
tests/

# Test data
 testdata/
