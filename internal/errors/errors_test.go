package errors

import (
	"testing"
)

func TestValidateNotNil(t *testing.T) {
	tests := []struct {
		name        string
		value       interface{}
		paramName   string
		expectError bool
	}{
		{
			name:        "nil value",
			value:       nil,
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "non-nil value",
			value:       "test",
			paramName:   "testParam",
			expectError: false,
		},
		{
			name:        "empty string but not nil",
			value:       "",
			paramName:   "testParam",
			expectError: false,
		},
		{
			name:        "zero value but not nil",
			value:       0,
			paramName:   "testParam",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateNotNil(tt.value, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("ValidateNotNil(%v, %q) expected error but got none", tt.value, tt.paramName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateNotNil(%v, %q) unexpected error: %v", tt.value, tt.paramName, err)
			}

			// If we expect an error, check that it's the right type and contains the parameter name
			if tt.expectError && err != nil {
				var appErr *AppError
				if !As(err, &appErr) {
					t.Errorf("ValidateNotNil(%v, %q) error is not an AppError: %v", tt.value, tt.paramName, err)
				}
				if appErr.Type != ValidationError {
					t.Errorf("ValidateNotNil(%v, %q) error type = %v, want %v", tt.value, tt.paramName, appErr.Type, ValidationError)
				}
				if appErr.Message != tt.paramName+" cannot be nil" {
					t.Errorf("ValidateNotNil(%v, %q) error message = %q, want %q", tt.value, tt.paramName, appErr.Message, tt.paramName+" cannot be nil")
				}
			}
		})
	}
}

func TestValidateNotEmpty(t *testing.T) {
	tests := []struct {
		name        string
		value       string
		paramName   string
		expectError bool
	}{
		{
			name:        "empty string",
			value:       "",
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "non-empty string",
			value:       "test",
			paramName:   "testParam",
			expectError: false,
		},
		{
			name:        "whitespace string",
			value:       " ",
			paramName:   "testParam",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateNotEmpty(tt.value, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("ValidateNotEmpty(%q, %q) expected error but got none", tt.value, tt.paramName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateNotEmpty(%q, %q) unexpected error: %v", tt.value, tt.paramName, err)
			}

			// If we expect an error, check that it's the right type and contains the parameter name
			if tt.expectError && err != nil {
				var appErr *AppError
				if !As(err, &appErr) {
					t.Errorf("ValidateNotEmpty(%q, %q) error is not an AppError: %v", tt.value, tt.paramName, err)
				}
				if appErr.Type != ValidationError {
					t.Errorf("ValidateNotEmpty(%q, %q) error type = %v, want %v", tt.value, tt.paramName, appErr.Type, ValidationError)
				}
				if appErr.Message != tt.paramName+" cannot be empty" {
					t.Errorf("ValidateNotEmpty(%q, %q) error message = %q, want %q", tt.value, tt.paramName, appErr.Message, tt.paramName+" cannot be empty")
				}
			}
		})
	}
}

func TestValidatePositive(t *testing.T) {
	tests := []struct {
		name        string
		value       int
		paramName   string
		expectError bool
	}{
		{
			name:        "negative value",
			value:       -1,
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "zero value",
			value:       0,
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "positive value",
			value:       1,
			paramName:   "testParam",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidatePositive(tt.value, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("ValidatePositive(%d, %q) expected error but got none", tt.value, tt.paramName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidatePositive(%d, %q) unexpected error: %v", tt.value, tt.paramName, err)
			}

			// If we expect an error, check that it's the right type and contains the parameter name
			if tt.expectError && err != nil {
				var appErr *AppError
				if !As(err, &appErr) {
					t.Errorf("ValidatePositive(%d, %q) error is not an AppError: %v", tt.value, tt.paramName, err)
				}
				if appErr.Type != ValidationError {
					t.Errorf("ValidatePositive(%d, %q) error type = %v, want %v", tt.value, tt.paramName, appErr.Type, ValidationError)
				}
				if appErr.Message != tt.paramName+" must be positive" {
					t.Errorf("ValidatePositive(%d, %q) error message = %q, want %q", tt.value, tt.paramName, appErr.Message, tt.paramName+" must be positive")
				}
			}
		})
	}
}

func TestValidateOneOf(t *testing.T) {
	tests := []struct {
		name        string
		value       string
		validValues []string
		paramName   string
		expectError bool
	}{
		{
			name:        "value in valid values",
			value:       "apple",
			validValues: []string{"apple", "banana", "orange"},
			paramName:   "testParam",
			expectError: false,
		},
		{
			name:        "value not in valid values",
			value:       "grape",
			validValues: []string{"apple", "banana", "orange"},
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "empty value not in valid values",
			value:       "",
			validValues: []string{"apple", "banana", "orange"},
			paramName:   "testParam",
			expectError: true,
		},
		{
			name:        "empty value in valid values",
			value:       "",
			validValues: []string{"", "apple", "banana"},
			paramName:   "testParam",
			expectError: false,
		},
		{
			name:        "empty valid values",
			value:       "apple",
			validValues: []string{},
			paramName:   "testParam",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateOneOf(tt.value, tt.validValues, tt.paramName)

			if tt.expectError && err == nil {
				t.Errorf("ValidateOneOf(%q, %v, %q) expected error but got none", tt.value, tt.validValues, tt.paramName)
			}
			if !tt.expectError && err != nil {
				t.Errorf("ValidateOneOf(%q, %v, %q) unexpected error: %v", tt.value, tt.validValues, tt.paramName, err)
			}

			// If we expect an error, check that it's the right type
			if tt.expectError && err != nil {
				var appErr *AppError
				if !As(err, &appErr) {
					t.Errorf("ValidateOneOf(%q, %v, %q) error is not an AppError: %v", tt.value, tt.validValues, tt.paramName, err)
				}
				if appErr.Type != ValidationError {
					t.Errorf("ValidateOneOf(%q, %v, %q) error type = %v, want %v", tt.value, tt.validValues, tt.paramName, appErr.Type, ValidationError)
				}
			}
		})
	}
}
