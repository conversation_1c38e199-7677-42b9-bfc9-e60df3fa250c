package errors

import (
	"errors"
	"fmt"
	"runtime"
	"strings"

	"github.com/rs/zerolog/log"
)

// ErrorType represents the type of error
type ErrorType string

const (
	// Configuration errors
	ConfigError ErrorType = "CONFIG_ERROR"

	// Network errors
	NetworkError ErrorType = "NETWORK_ERROR"

	// SNMP errors
	SNMPError ErrorType = "SNMP_ERROR"

	// Parsing errors
	ParseError ErrorType = "PARSE_ERROR"

	// Validation errors
	ValidationError ErrorType = "VALIDATION_ERROR"

	// Template errors
	TemplateError ErrorType = "TEMPLATE_ERROR"

	// Output errors
	OutputError ErrorType = "OUTPUT_ERROR"

	// Internal errors
	InternalError ErrorType = "INTERNAL_ERROR"
)

// AppError represents an application error with context
type AppError struct {
	Type      ErrorType
	Message   string
	Err       error
	Source    string
	Line      int
	Metadata  map[string]interface{}
	Retriable bool
}

// Error implements the error interface
func (e *AppError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("%s: %s: %v", e.Type, e.Message, e.Err)
	}
	return fmt.Sprintf("%s: %s", e.Type, e.Message)
}

// Unwrap returns the wrapped error
func (e *AppError) Unwrap() error {
	return e.Err
}

// WithMetadata adds metadata to the error
func (e *AppError) WithMetadata(key string, value interface{}) *AppError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// New creates a new AppError
func New(errType ErrorType, message string) *AppError {
	_, file, line, _ := runtime.Caller(1)
	source := getSourceFromFile(file)

	return &AppError{
		Type:      errType,
		Message:   message,
		Source:    source,
		Line:      line,
		Metadata:  make(map[string]interface{}),
		Retriable: false,
	}
}

// Wrap wraps an existing error with additional context
func Wrap(err error, errType ErrorType, message string) *AppError {
	_, file, line, _ := runtime.Caller(1)
	source := getSourceFromFile(file)

	return &AppError{
		Type:      errType,
		Message:   message,
		Err:       err,
		Source:    source,
		Line:      line,
		Metadata:  make(map[string]interface{}),
		Retriable: false,
	}
}

// IsRetriable returns true if the error is retriable
func IsRetriable(err error) bool {
	var appErr *AppError
	if As(err, &appErr) {
		return appErr.Retriable
	}
	return false
}

// SetRetriable marks an error as retriable
func SetRetriable(err error, retriable bool) *AppError {
	var appErr *AppError
	if As(err, &appErr) {
		appErr.Retriable = retriable
		return appErr
	}
	return Wrap(err, InternalError, "Unknown error").WithRetriable(retriable)
}

// WithRetriable sets the retriable flag on an AppError
func (e *AppError) WithRetriable(retriable bool) *AppError {
	e.Retriable = retriable
	return e
}

// LogError logs an error with context
func LogError(err error) {
	var appErr *AppError
	if As(err, &appErr) {
		logEvent := log.Error().
			Str("error_type", string(appErr.Type)).
			Str("message", appErr.Message).
			Str("source", appErr.Source).
			Int("line", appErr.Line).
			Bool("retriable", appErr.Retriable)

		// Add metadata if available
		for k, v := range appErr.Metadata {
			logEvent = logEvent.Interface(k, v)
		}

		// Add wrapped error if available
		if appErr.Err != nil {
			logEvent = logEvent.Err(appErr.Err)
		}

		logEvent.Msg("Application error")
	} else {
		log.Error().Err(err).Msg("Unknown error")
	}
}

// As is a wrapper around errors.As
func As(err error, target interface{}) bool {
	return errors.As(err, target)
}

// Is is a wrapper around errors.Is
func Is(err, target error) bool {
	return errors.Is(err, target)
}

// getSourceFromFile extracts the package and file name from a file path
func getSourceFromFile(file string) string {
	parts := strings.Split(file, "/")
	if len(parts) < 2 {
		parts = strings.Split(file, "\\")
	}

	if len(parts) < 2 {
		return file
	}

	// Return the last two parts (package and file)
	return strings.Join(parts[len(parts)-2:], "/")
}

// HandleError handles an error based on its type and context
func HandleError(err error, fatal bool) {
	LogError(err)

	if fatal {
		log.Fatal().Msg("Fatal error, exiting")
	}
}

// ValidateNotNil validates that a value is not nil
func ValidateNotNil(value interface{}, name string) error {
	if value == nil {
		return New(ValidationError, fmt.Sprintf("%s cannot be nil", name))
	}
	return nil
}

// ValidateNotEmpty validates that a string is not empty
func ValidateNotEmpty(value string, name string) error {
	if value == "" {
		return New(ValidationError, fmt.Sprintf("%s cannot be empty", name))
	}
	return nil
}

// ValidatePositive validates that an integer is positive
func ValidatePositive(value int, name string) error {
	if value <= 0 {
		return New(ValidationError, fmt.Sprintf("%s must be positive", name))
	}
	return nil
}

// ValidateInRange validates that an integer is within a range
func ValidateInRange(value int, min int, max int, name string) error {
	if value < min || value > max {
		return New(ValidationError, fmt.Sprintf("%s must be between %d and %d", name, min, max))
	}
	return nil
}

// ValidateOneOf validates that a string is one of a set of values
func ValidateOneOf(value string, validValues []string, name string) error {
	for _, v := range validValues {
		if value == v {
			return nil
		}
	}
	return New(ValidationError, fmt.Sprintf("%s must be one of %v", name, validValues))
}
