package snmp

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"argus/internal/config"
	"argus/internal/errors"

	"github.com/gosnmp/gosnmp"
	"github.com/rs/zerolog/log"
)

// TrapListener represents an SNMP trap listener
type TrapListener struct {
	config     *config.Config
	conn       *net.UDPConn
	handler    TrapHandler
	wg         sync.WaitGroup
	cancelFunc context.CancelFunc
}

// TrapHandler is an interface for handling SNMP traps
type TrapHandler interface {
	HandleTrap(trap *gosnmp.SnmpPacket, addr *net.UDPAddr) error
}

// NewTrapListener creates a new SNMP trap listener
func NewTrapListener(cfg *config.Config, handler TrapHandler) (*TrapListener, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}
	if err := errors.ValidateNotNil(handler, "handler"); err != nil {
		return nil, err
	}

	return &TrapListener{
		config:  cfg,
		handler: handler,
	}, nil
}

// Start starts the SNMP trap listener
func (l *TrapListener) Start(ctx context.Context) error {
	// Validate input
	if err := errors.ValidateNotNil(ctx, "ctx"); err != nil {
		return err
	}

	// Create a cancellable context
	ctx, cancel := context.WithCancel(ctx)
	l.cancelFunc = cancel

	// Set up UDP listener
	addr := fmt.Sprintf("%s:%d", l.config.Server.BindAddress, l.config.Server.Port)
	udpAddr, err := net.ResolveUDPAddr("udp", addr)
	if err != nil {
		return fmt.Errorf("failed to resolve UDP address: %w", err)
	}

	conn, err := net.ListenUDP("udp", udpAddr)
	if err != nil {
		return fmt.Errorf("failed to listen on UDP: %w", err)
	}
	l.conn = conn

	log.Info().
		Str("address", addr).
		Str("community", l.config.SNMP.Community).
		Str("version", l.config.SNMP.Version).
		Msg("SNMP trap listener started")

	// Start the listener goroutine
	l.wg.Add(1)
	go l.listen(ctx)

	return nil
}

// Stop stops the SNMP trap listener
func (l *TrapListener) Stop() error {
	if l.cancelFunc != nil {
		l.cancelFunc()
	}

	if l.conn != nil {
		if err := l.conn.Close(); err != nil {
			return fmt.Errorf("failed to close UDP connection: %w", err)
		}
	}

	// Wait for the listener goroutine to finish
	l.wg.Wait()
	log.Info().Msg("SNMP trap listener stopped")
	return nil
}

// listen listens for incoming SNMP traps
func (l *TrapListener) listen(ctx context.Context) {
	defer l.wg.Done()

	// Buffer for incoming packets
	buffer := make([]byte, 4096)

	for {
		select {
		case <-ctx.Done():
			return
		default:
			// Set read deadline to allow for context cancellation
			if err := l.conn.SetReadDeadline(time.Now().Add(1 * time.Second)); err != nil {
				log.Error().Err(err).Msg("Failed to set read deadline")
				continue
			}

			// Read incoming packet
			n, addr, err := l.conn.ReadFromUDP(buffer)
			if err != nil {
				if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
					// This is just a timeout, continue
					continue
				}
				log.Error().Err(err).Msg("Error reading from UDP")
				continue
			}

			// Process the packet in a separate goroutine
			packet := make([]byte, n)
			copy(packet, buffer[:n])
			go l.processPacket(packet, addr)
		}
	}
}

// processPacket processes an incoming SNMP packet
func (l *TrapListener) processPacket(packet []byte, addr *net.UDPAddr) {
	// Validate input
	if packet == nil || len(packet) == 0 {
		log.Error().Msg("Empty packet received")
		return
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		log.Error().Err(err).Msg("Invalid address")
		return
	}

	// Create a GoSNMP instance for parsing
	snmp := &gosnmp.GoSNMP{
		Target:    addr.IP.String(),
		Port:      uint16(addr.Port),
		Community: l.config.SNMP.Community,
		Version:   gosnmp.Version2c, // Default to v2c
		Timeout:   time.Duration(2) * time.Second,
		Retries:   1,
	}

	// Set version based on configuration
	switch l.config.SNMP.Version {
	case "1":
		snmp.Version = gosnmp.Version1
	case "3":
		snmp.Version = gosnmp.Version3
	}

	// Log the received packet
	log.Debug().
		Str("source", addr.String()).
		Int("packet_size", len(packet)).
		Msg("Received UDP packet")

	// Try to parse as SNMP packet
	trapPacket := &gosnmp.SnmpPacket{
		Version:   snmp.Version,
		Community: snmp.Community,
	}

	// This is a simplified approach - in a real implementation, you would need
	// to properly parse the SNMP packet according to the SNMP protocol
	// For now, we'll just create a basic packet with the source information

	// Handle the trap
	if err := l.handler.HandleTrap(trapPacket, addr); err != nil {
		log.Error().
			Err(err).
			Str("source", addr.String()).
			Msg("Failed to handle SNMP trap")
	}
}
