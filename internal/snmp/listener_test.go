package snmp

import (
	"net"
	"testing"

	"argus/internal/config"

	"github.com/gosnmp/gosnmp"
)

// MockTrapHandler is a mock implementation of TrapHandler for testing
type MockTrapHandler struct {
	lastTrap    *gosnmp.SnmpPacket
	lastAddr    *net.UDPAddr
	callCount   int
	shouldError bool
}

func (m *MockTrapHandler) HandleTrap(trap *gosnmp.SnmpPacket, addr *net.UDPAddr) error {
	m.lastTrap = trap
	m.lastAddr = addr
	m.callCount++
	if m.shouldError {
		return &net.OpError{Op: "test", Err: net.ErrClosed}
	}
	return nil
}

func TestNewTrapListener(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port:        1162,
			BindAddress: "127.0.0.1",
		},
		SNMP: config.SNMPConfig{
			Community: "public",
			Version:   "2c",
		},
	}

	handler := &MockTrapHandler{}

	tests := []struct {
		name        string
		cfg         *config.Config
		handler     TrapHandler
		expectError bool
	}{
		{
			name:        "valid configuration",
			cfg:         cfg,
			handler:     handler,
			expectError: false,
		},
		{
			name:        "nil configuration",
			cfg:         nil,
			handler:     handler,
			expectError: true,
		},
		{
			name:        "nil handler",
			cfg:         cfg,
			handler:     nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			listener, err := NewTrapListener(tt.cfg, tt.handler)

			if tt.expectError && err == nil {
				t.Errorf("NewTrapListener() expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("NewTrapListener() unexpected error: %v", err)
			}
			if !tt.expectError && listener == nil {
				t.Errorf("NewTrapListener() returned nil listener")
			}
		})
	}
}

func TestProcessPacketBasic(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port:        1162,
			BindAddress: "127.0.0.1",
		},
		SNMP: config.SNMPConfig{
			Community: "public",
			Version:   "2c",
		},
	}

	handler := &MockTrapHandler{}
	listener, err := NewTrapListener(cfg, handler)
	if err != nil {
		t.Fatalf("Failed to create listener: %v", err)
	}

	addr := &net.UDPAddr{
		IP:   net.ParseIP("*************"),
		Port: 12345,
	}

	tests := []struct {
		name          string
		packet        []byte
		addr          *net.UDPAddr
		expectHandled bool
	}{
		{
			name:          "empty packet",
			packet:        []byte{},
			addr:          addr,
			expectHandled: false,
		},
		{
			name:          "nil packet",
			packet:        nil,
			addr:          addr,
			expectHandled: false,
		},
		{
			name:          "nil address",
			packet:        []byte{0x01, 0x02},
			addr:          nil,
			expectHandled: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset handler state
			handler.lastTrap = nil
			handler.lastAddr = nil
			handler.callCount = 0

			// Process the packet
			listener.processPacket(tt.packet, tt.addr)

			// Check if handler was called as expected
			if tt.expectHandled && handler.callCount == 0 {
				t.Errorf("processPacket() expected handler to be called but it wasn't")
			}
			if !tt.expectHandled && handler.callCount > 0 {
				t.Errorf("processPacket() expected handler not to be called but it was called %d times", handler.callCount)
			}
		})
	}
}

func TestTryParseWithVersions(t *testing.T) {
	cfg := &config.Config{
		Server: config.ServerConfig{
			Port:        1162,
			BindAddress: "127.0.0.1",
		},
		SNMP: config.SNMPConfig{
			Community: "public",
			Version:   "2c",
		},
	}

	handler := &MockTrapHandler{}
	listener, err := NewTrapListener(cfg, handler)
	if err != nil {
		t.Fatalf("Failed to create listener: %v", err)
	}

	addr := &net.UDPAddr{
		IP:   net.ParseIP("*************"),
		Port: 12345,
	}

	tests := []struct {
		name          string
		packet        []byte
		expectSuccess bool
	}{
		{
			name:          "invalid packet",
			packet:        []byte{0x01, 0x02, 0x03},
			expectSuccess: false,
		},
		{
			name:          "empty packet",
			packet:        []byte{},
			expectSuccess: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := listener.tryParseWithVersions(tt.packet, addr)

			if tt.expectSuccess {
				if err != nil {
					t.Errorf("tryParseWithVersions() unexpected error: %v", err)
				}
				if result == nil {
					t.Errorf("tryParseWithVersions() returned nil result")
				}
			} else {
				if err == nil {
					t.Errorf("tryParseWithVersions() expected error but got none")
				}
			}
		})
	}
}
