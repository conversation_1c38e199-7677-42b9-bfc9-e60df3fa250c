package snmp

import (
	"net"
	"testing"

	"argus/internal/config"
	"argus/internal/errors"

	"github.com/gosnmp/gosnmp"
)

func TestTrapListener_validatePacket(t *testing.T) {
	// Create a test configuration
	cfg := &config.Config{
		SNMP: config.SNMPConfig{
			Community: "public",
			Version:   "2c",
		},
	}

	// Create a test listener
	listener := &TrapListener{
		config: cfg,
	}

	// Test address
	addr, _ := net.ResolveUDPAddr("udp", "127.0.0.1:162")

	tests := []struct {
		name        string
		packet      []byte
		expectError bool
		errorType   errors.ErrorType
	}{
		{
			name:        "empty packet",
			packet:      []byte{},
			expectError: true,
			errorType:   errors.ValidationError,
		},
		{
			name:        "packet too small",
			packet:      []byte{0x30, 0x05},
			expectError: true,
			errorType:   errors.ValidationError,
		},
		{
			name:        "packet too large",
			packet:      make([]byte, 10000),
			expectError: true,
			errorType:   errors.ValidationError,
		},
		{
			name:        "invalid ASN.1 tag",
			packet:      []byte{0x31, 0x10, 0x02, 0x01, 0x00, 0x04, 0x06, 0x70, 0x75, 0x62, 0x6c, 0x69, 0x63, 0xa7, 0x03, 0x02, 0x01, 0x00},
			expectError: true,
			errorType:   errors.ValidationError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := listener.validatePacket(tt.packet, addr)
			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
					return
				}
				if appErr, ok := err.(*errors.AppError); ok {
					if appErr.Type != tt.errorType {
						t.Errorf("Expected error type %v, got %v", tt.errorType, appErr.Type)
					}
				} else {
					t.Errorf("Expected AppError, got %T", err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error but got: %v", err)
				}
			}
		})
	}
}

func TestTrapListener_validateASN1Structure(t *testing.T) {
	listener := &TrapListener{}

	tests := []struct {
		name        string
		packet      []byte
		expectError bool
	}{
		{
			name:        "valid short form length",
			packet:      []byte{0x30, 0x03, 0x02, 0x01, 0x00},
			expectError: false,
		},
		{
			name:        "packet too short",
			packet:      []byte{0x30},
			expectError: true,
		},
		{
			name:        "invalid tag",
			packet:      []byte{0x31, 0x10},
			expectError: true,
		},
		{
			name:        "indefinite length",
			packet:      []byte{0x30, 0x80},
			expectError: true,
		},
		{
			name:        "length encoding too long",
			packet:      []byte{0x30, 0x85, 0x01, 0x02, 0x03, 0x04, 0x05},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := listener.validateASN1Structure(tt.packet)
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			} else if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestTrapListener_validateVariableBindings(t *testing.T) {
	listener := &TrapListener{}

	tests := []struct {
		name        string
		variables   []gosnmp.SnmpPDU
		expectError bool
	}{
		{
			name: "valid variables",
			variables: []gosnmp.SnmpPDU{
				{Name: ".1.3.6.1.2.1.1.3.0", Type: gosnmp.TimeTicks, Value: uint32(12345)},
				{Name: ".1.3.6.1.6.3.1.1.4.1.0", Type: gosnmp.ObjectIdentifier, Value: ".1.3.6.1.4.1.9.9.41.2.0.1"},
			},
			expectError: false,
		},
		{
			name:        "too many variables",
			variables:   make([]gosnmp.SnmpPDU, 101),
			expectError: true,
		},
		{
			name: "empty OID",
			variables: []gosnmp.SnmpPDU{
				{Name: "", Type: gosnmp.Integer, Value: 123},
			},
			expectError: true,
		},
		{
			name: "invalid OID format",
			variables: []gosnmp.SnmpPDU{
				{Name: "invalid-oid", Type: gosnmp.Integer, Value: 123},
			},
			expectError: true,
		},
		{
			name: "null value for non-null type",
			variables: []gosnmp.SnmpPDU{
				{Name: ".1.3.6.1.2.1.1.3.0", Type: gosnmp.Integer, Value: nil},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := listener.validateVariableBindings(tt.variables)
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			} else if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}

func TestTrapListener_validateVariableType(t *testing.T) {
	listener := &TrapListener{}

	tests := []struct {
		name        string
		varType     gosnmp.Asn1BER
		expectError bool
	}{
		{name: "Integer", varType: gosnmp.Integer, expectError: false},
		{name: "OctetString", varType: gosnmp.OctetString, expectError: false},
		{name: "ObjectIdentifier", varType: gosnmp.ObjectIdentifier, expectError: false},
		{name: "IPAddress", varType: gosnmp.IPAddress, expectError: false},
		{name: "Counter32", varType: gosnmp.Counter32, expectError: false},
		{name: "Gauge32", varType: gosnmp.Gauge32, expectError: false},
		{name: "TimeTicks", varType: gosnmp.TimeTicks, expectError: false},
		{name: "Counter64", varType: gosnmp.Counter64, expectError: false},
		{name: "Null", varType: gosnmp.Null, expectError: false},
		{name: "NoSuchObject", varType: gosnmp.NoSuchObject, expectError: false},
		{name: "NoSuchInstance", varType: gosnmp.NoSuchInstance, expectError: false},
		{name: "EndOfMibView", varType: gosnmp.EndOfMibView, expectError: false},
		{name: "Unsupported type", varType: gosnmp.Asn1BER(255), expectError: true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := listener.validateVariableType(tt.varType)
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
			} else if !tt.expectError && err != nil {
				t.Errorf("Expected no error but got: %v", err)
			}
		})
	}
}
