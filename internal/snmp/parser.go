package snmp

import (
	"fmt"
	"net"
	"strings"
	"time"

	"argus/internal/errors"

	"github.com/gosnmp/gosnmp"
	"github.com/rs/zerolog/log"
)

// TrapMessage represents a parsed SNMP trap message
type TrapMessage struct {
	SourceIP  string                 `json:"source_ip"`
	Community string                 `json:"community"`
	Version   string                 `json:"version"`
	TrapType  string                 `json:"trap_type"`
	OID       string                 `json:"oid"`
	Variables []TrapVariable         `json:"variables"`
	Timestamp time.Time              `json:"timestamp"`
	RawData   map[string]interface{} `json:"raw_data"`
}

// TrapVariable represents a variable binding in an SNMP trap
type TrapVariable struct {
	OID         string      `json:"oid"`
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Value       interface{} `json:"value"`
	DisplayHint string      `json:"display_hint,omitempty"`
}

// TrapParser handles parsing of SNMP trap messages
type TrapParser struct {
	// Add fields for MIB handling, etc.
}

// NewTrapParser creates a new SNMP trap parser
func NewTrapParser() *TrapParser {
	return &TrapParser{}
}

// ParseTrap parses an SNMP trap packet into a TrapMessage
func (p *TrapParser) ParseTrap(packet *gosnmp.SnmpPacket, addr *net.UDPAddr) (*TrapMessage, error) {
	// Validate input
	if err := errors.ValidateNotNil(packet, "packet"); err != nil {
		return nil, err
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		return nil, err
	}

	// Create a new trap message
	msg := &TrapMessage{
		SourceIP:  addr.IP.String(),
		Community: packet.Community,
		Version:   getVersionString(packet.Version),
		Timestamp: time.Now(),
		RawData:   make(map[string]interface{}),
	}

	// Set trap type and OID based on PDU type
	switch packet.PDUType {
	case gosnmp.Trap:
		msg.TrapType = "SNMPv1Trap"
		// For SNMPv1 traps, the enterprise OID is in a different location
		if len(packet.Variables) > 0 {
			msg.OID = packet.Variables[0].Name
		} else {
			log.Warn().
				Str("source", addr.String()).
				Msg("SNMPv1 trap has no variables")
		}
	case gosnmp.SNMPv2Trap:
		msg.TrapType = "SNMPv2Trap"
		// For SNMPv2 traps, look for the snmpTrapOID.0 variable
		trapOIDFound := false
		for _, v := range packet.Variables {
			if v.Name == ".*******.*******.4.1.0" || v.Name == "*******.*******.4.1.0" {
				if oid, ok := v.Value.(string); ok {
					msg.OID = oid
					trapOIDFound = true
				} else {
					log.Warn().
						Str("source", addr.String()).
						Interface("value", v.Value).
						Msg("SNMPv2 trap OID is not a string")
				}
			}
		}
		if !trapOIDFound {
			log.Warn().
				Str("source", addr.String()).
				Msg("SNMPv2 trap does not contain snmpTrapOID.0")
		}
	default:
		msg.TrapType = fmt.Sprintf("Unknown (%d)", packet.PDUType)
		log.Warn().
			Str("source", addr.String()).
			Int("pdu_type", int(packet.PDUType)).
			Msg("Unknown PDU type")
	}

	// Parse variables
	for _, v := range packet.Variables {
		name, err := getOIDName(v.Name)
		if err != nil {
			log.Warn().
				Err(err).
				Str("oid", v.Name).
				Msg("Failed to get OID name, using OID as name")
			name = v.Name
		}

		variable := TrapVariable{
			OID:   v.Name,
			Name:  name,
			Type:  getVariableType(v.Type),
			Value: formatVariableValue(v.Type, v.Value),
		}
		msg.Variables = append(msg.Variables, variable)

		// Add to raw data for easy access
		msg.RawData[variable.Name] = variable.Value
	}

	log.Debug().
		Str("source", addr.String()).
		Str("trap_type", msg.TrapType).
		Str("oid", msg.OID).
		Int("variables", len(msg.Variables)).
		Msg("Parsed SNMP trap")

	return msg, nil
}

// getVersionString converts a gosnmp.SnmpVersion to a string
func getVersionString(version gosnmp.SnmpVersion) string {
	switch version {
	case gosnmp.Version1:
		return "SNMPv1"
	case gosnmp.Version2c:
		return "SNMPv2c"
	case gosnmp.Version3:
		return "SNMPv3"
	default:
		return fmt.Sprintf("Unknown (%d)", version)
	}
}

// getOIDName converts an OID to a human-readable name
// In a real implementation, this would use MIB files for translation
func getOIDName(oid string) (string, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(oid, "oid"); err != nil {
		return "", err
	}

	// Remove leading dot if present
	cleanOID := oid
	if strings.HasPrefix(cleanOID, ".") {
		cleanOID = cleanOID[1:]
	}

	// This is a simplified implementation
	// In a real implementation, you would use MIB files to translate OIDs
	knownOIDs := map[string]string{
		"*******.2.1.1.3.0":     "sysUpTime.0",
		"*******.*******.4.1.0": "snmpTrapOID.0",
		"*******.2.1.2.2.1.1":   "ifIndex",
		"*******.2.1.2.2.1.2":   "ifDescr",
		"*******.2.1.2.2.1.3":   "ifType",
		"*******.2.1.2.2.1.7":   "ifAdminStatus",
		"*******.2.1.2.2.1.8":   "ifOperStatus",
	}

	// Check if this is a known OID
	for prefix, name := range knownOIDs {
		if strings.HasPrefix(cleanOID, prefix) {
			// If it's an exact match, return the name
			if cleanOID == prefix {
				log.Debug().
					Str("oid", oid).
					Str("name", name).
					Msg("Translated OID to name (exact match)")
				return name, nil
			}
			// If it's a prefix match, append the remaining part
			suffix := cleanOID[len(prefix):]
			var result string
			if strings.HasPrefix(suffix, ".") {
				result = name + suffix
			} else {
				result = name + "." + suffix
			}
			log.Debug().
				Str("oid", oid).
				Str("name", result).
				Msg("Translated OID to name (prefix match)")
			return result, nil
		}
	}

	// If not found, return the original OID
	log.Debug().
		Str("oid", oid).
		Msg("No translation found for OID")
	return oid, nil
}

// getVariableType converts a gosnmp.Asn1BER to a string
func getVariableType(varType gosnmp.Asn1BER) string {
	switch varType {
	case gosnmp.Integer:
		return "Integer"
	case gosnmp.OctetString:
		return "OctetString"
	case gosnmp.ObjectIdentifier:
		return "ObjectIdentifier"
	case gosnmp.IPAddress:
		return "IPAddress"
	case gosnmp.Counter32:
		return "Counter32"
	case gosnmp.Gauge32:
		return "Gauge32"
	case gosnmp.TimeTicks:
		return "TimeTicks"
	case gosnmp.Counter64:
		return "Counter64"
	case gosnmp.Null:
		return "Null"
	case gosnmp.NoSuchObject:
		return "NoSuchObject"
	case gosnmp.NoSuchInstance:
		return "NoSuchInstance"
	case gosnmp.EndOfMibView:
		return "EndOfMibView"
	default:
		return fmt.Sprintf("Unknown (%d)", varType)
	}
}

// formatVariableValue formats a variable value based on its type
func formatVariableValue(varType gosnmp.Asn1BER, value interface{}) interface{} {
	switch varType {
	case gosnmp.TimeTicks:
		// Convert TimeTicks to a human-readable format
		if ticks, ok := value.(uint32); ok {
			seconds := float64(ticks) / 100.0
			d := time.Duration(seconds * float64(time.Second))
			return fmt.Sprintf("%s (%d)", d.String(), ticks)
		}
	case gosnmp.OctetString:
		// Try to convert OctetString to a string if it's printable
		if bytes, ok := value.([]byte); ok {
			return string(bytes)
		}
	}

	// For other types, return as is
	return value
}

// FormatOID formats an OID by inserting dots between numbers
// This is a utility function that can be used to fix improperly formatted OIDs
func FormatOID(oid string) (string, error) {
	// Validate input
	if oid == "" {
		return "", errors.New(errors.ValidationError, "OID cannot be empty")
	}

	// If already formatted, return as is
	if strings.Contains(oid, ".") {
		return oid, nil
	}

	// Validate that the OID contains only digits
	for _, c := range oid {
		if c < '0' || c > '9' {
			return "", errors.New(errors.ValidationError, "OID must contain only digits").
				WithMetadata("oid", oid)
		}
	}

	// Insert dots between numbers
	var result strings.Builder
	for i, c := range oid {
		if i > 0 {
			result.WriteRune('.')
		}
		result.WriteRune(c)
	}

	formattedOID := result.String()
	log.Debug().
		Str("original", oid).
		Str("formatted", formattedOID).
		Msg("Formatted OID")

	return formattedOID, nil
}
