package snmp

import (
	"context"
	"fmt"
	"log/slog"
	"net"
	"strings"
	"time"

	"argus/internal/errors"
	"argus/internal/translator"

	"github.com/gosnmp/gosnmp"
)

// TrapMessage represents a parsed SNMP trap message
type TrapMessage struct {
	SourceIP  string                 `json:"source_ip"`
	Community string                 `json:"community"`
	Version   string                 `json:"version"`
	TrapType  string                 `json:"trap_type"`
	OID       string                 `json:"oid"`
	Variables []TrapVariable         `json:"variables"`
	Timestamp time.Time              `json:"timestamp"`
	RawData   map[string]interface{} `json:"raw_data"`
}

// TrapVariable represents a variable binding in an SNMP trap
type TrapVariable struct {
	OID         string      `json:"oid"`
	Name        string      `json:"name"`
	Type        string      `json:"type"`
	Value       interface{} `json:"value"`
	DisplayHint string      `json:"display_hint,omitempty"`
}

// TrapParser handles parsing of SNMP trap messages
type TrapParser struct {
	translator translator.Translator
}

// NewTrapParser creates a new SNMP trap parser
func NewTrapParser() *TrapParser {
	return &TrapParser{}
}

// NewTrapParserWithTranslator creates a new SNMP trap parser with a translator
func NewTrapParserWithTranslator(t translator.Translator) *TrapParser {
	return &TrapParser{
		translator: t,
	}
}

// ParseTrap parses an SNMP trap packet into a TrapMessage
func (p *TrapParser) ParseTrap(packet *gosnmp.SnmpPacket, addr *net.UDPAddr) (*TrapMessage, error) {
	// Validate input
	if err := errors.ValidateNotNil(packet, "packet"); err != nil {
		return nil, err
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		return nil, err
	}

	// Create a new trap message
	msg := &TrapMessage{
		SourceIP:  addr.IP.String(),
		Community: packet.Community,
		Version:   getVersionString(packet.Version),
		Timestamp: time.Now(),
		RawData:   make(map[string]interface{}),
	}

	// Set trap type and OID based on PDU type
	switch packet.PDUType {
	case gosnmp.Trap:
		msg.TrapType = "SNMPv1Trap"
		// For SNMPv1 traps, the enterprise OID is in the Enterprise field, not in variables
		if packet.Enterprise != "" {
			msg.OID = packet.Enterprise
		} else {
			slog.Warn("SNMPv1 trap has no enterprise OID", "source", addr.String())
		}

		// Add SNMPv1-specific trap information to raw data
		msg.RawData["enterprise"] = packet.Enterprise
		msg.RawData["agentAddress"] = packet.AgentAddress
		msg.RawData["genericTrap"] = packet.GenericTrap
		msg.RawData["specificTrap"] = packet.SpecificTrap
		msg.RawData["timestamp"] = packet.Timestamp
	case gosnmp.SNMPv2Trap:
		msg.TrapType = "SNMPv2Trap"
		// For SNMPv2 traps, look for the snmpTrapOID.0 variable
		trapOIDFound := false
		for _, v := range packet.Variables {
			if v.Name == ".*******.*******.4.1.0" || v.Name == "*******.*******.4.1.0" {
				// Handle different value types for the trap OID
				switch val := v.Value.(type) {
				case string:
					msg.OID = val
					trapOIDFound = true
				case []byte:
					// Sometimes OIDs come as byte arrays
					msg.OID = string(val)
					trapOIDFound = true
				default:
					// Try to convert to string
					msg.OID = fmt.Sprintf("%v", val)
					trapOIDFound = true
					slog.Debug("SNMPv2 trap OID converted from non-string type",
						"source", addr.String(),
						"value", v.Value,
						"type", fmt.Sprintf("%T", v.Value))
				}
				break
			}
		}
		if !trapOIDFound {
			slog.Warn("SNMPv2 trap does not contain snmpTrapOID.0", "source", addr.String())
		}
	default:
		msg.TrapType = fmt.Sprintf("Unknown (%d)", packet.PDUType)
		slog.Warn("Unknown PDU type",
			"source", addr.String(),
			"pdu_type", int(packet.PDUType))
	}

	// Parse variables
	for _, v := range packet.Variables {
		name, err := p.getOIDName(v.Name)
		if err != nil {
			slog.Warn("Failed to get OID name, using OID as name",
				"error", err,
				"oid", v.Name)
			name = v.Name
		}

		variable := TrapVariable{
			OID:   v.Name,
			Name:  name,
			Type:  getVariableType(v.Type),
			Value: formatVariableValue(v.Type, v.Value),
		}
		msg.Variables = append(msg.Variables, variable)

		// Add to raw data for easy access
		msg.RawData[variable.Name] = variable.Value
	}

	slog.Debug("Parsed SNMP trap",
		"source", addr.String(),
		"trap_type", msg.TrapType,
		"oid", msg.OID,
		"variables", len(msg.Variables))

	return msg, nil
}

// getVersionString converts a gosnmp.SnmpVersion to a string
func getVersionString(version gosnmp.SnmpVersion) string {
	switch version {
	case gosnmp.Version1:
		return "SNMPv1"
	case gosnmp.Version2c:
		return "SNMPv2c"
	case gosnmp.Version3:
		return "SNMPv3"
	default:
		return fmt.Sprintf("Unknown (%d)", version)
	}
}

// getOIDName converts an OID to a human-readable name using the translator
func (p *TrapParser) getOIDName(oid string) (string, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(oid, "oid"); err != nil {
		return "", err
	}

	// If translator is available, use it
	if p.translator != nil {
		ctx := context.Background()
		name, err := p.translator.TranslateOID(ctx, oid)
		if err != nil {
			slog.Warn("Translator failed, using fallback",
				"error", err,
				"oid", oid)
		} else {
			return name, nil
		}
	}

	// Fallback to basic known OIDs
	return getFallbackOIDName(oid), nil
}

// getFallbackOIDName provides basic fallback OID translation
func getFallbackOIDName(oid string) string {
	// Remove leading dot if present
	cleanOID := oid
	if strings.HasPrefix(cleanOID, ".") {
		cleanOID = cleanOID[1:]
	}

	// Basic known OIDs for fallback
	knownOIDs := map[string]string{
		"*******.2.1.1.3.0":     "sysUpTime.0",
		"*******.*******.4.1.0": "snmpTrapOID.0",
		"*******.2.1.2.2.1.1":   "ifIndex",
		"*******.2.1.2.2.1.2":   "ifDescr",
		"*******.2.1.2.2.1.3":   "ifType",
		"*******.2.1.2.2.1.7":   "ifAdminStatus",
		"*******.2.1.2.2.1.8":   "ifOperStatus",
	}

	// Check if this is a known OID
	for prefix, name := range knownOIDs {
		if strings.HasPrefix(cleanOID, prefix) {
			// If it's an exact match, return the name
			if cleanOID == prefix {
				slog.Debug("Translated OID to name (exact match - fallback)",
					"oid", oid,
					"name", name)
				return name
			}
			// If it's a prefix match, append the remaining part
			suffix := cleanOID[len(prefix):]
			var result string
			if strings.HasPrefix(suffix, ".") {
				result = name + suffix
			} else {
				result = name + "." + suffix
			}
			slog.Debug("Translated OID to name (prefix match - fallback)",
				"oid", oid,
				"name", result)
			return result
		}
	}

	// If not found, return the original OID
	slog.Debug("No translation found for OID (fallback)", "oid", oid)
	return oid
}

// getVariableType converts a gosnmp.Asn1BER to a string
func getVariableType(varType gosnmp.Asn1BER) string {
	switch varType {
	case gosnmp.Integer:
		return "Integer"
	case gosnmp.OctetString:
		return "OctetString"
	case gosnmp.ObjectIdentifier:
		return "ObjectIdentifier"
	case gosnmp.IPAddress:
		return "IPAddress"
	case gosnmp.Counter32:
		return "Counter32"
	case gosnmp.Gauge32:
		return "Gauge32"
	case gosnmp.TimeTicks:
		return "TimeTicks"
	case gosnmp.Counter64:
		return "Counter64"
	case gosnmp.Null:
		return "Null"
	case gosnmp.NoSuchObject:
		return "NoSuchObject"
	case gosnmp.NoSuchInstance:
		return "NoSuchInstance"
	case gosnmp.EndOfMibView:
		return "EndOfMibView"
	default:
		return fmt.Sprintf("Unknown (%d)", varType)
	}
}

// formatVariableValue formats a variable value based on its type
func formatVariableValue(varType gosnmp.Asn1BER, value interface{}) interface{} {
	switch varType {
	case gosnmp.TimeTicks:
		// Convert TimeTicks to a human-readable format
		if ticks, ok := value.(uint32); ok {
			seconds := float64(ticks) / 100.0
			d := time.Duration(seconds * float64(time.Second))
			return fmt.Sprintf("%s (%d)", d.String(), ticks)
		}
	case gosnmp.OctetString:
		// Try to convert OctetString to a string if it's printable
		if bytes, ok := value.([]byte); ok {
			return string(bytes)
		}
	}

	// For other types, return as is
	return value
}

// FormatOID formats an OID by inserting dots between numbers
// This is a utility function that can be used to fix improperly formatted OIDs
func FormatOID(oid string) (string, error) {
	// Validate input
	if oid == "" {
		return "", errors.New(errors.ValidationError, "OID cannot be empty")
	}

	// If already formatted, return as is
	if strings.Contains(oid, ".") {
		return oid, nil
	}

	// Validate that the OID contains only digits
	for _, c := range oid {
		if c < '0' || c > '9' {
			return "", errors.New(errors.ValidationError, "OID must contain only digits").
				WithMetadata("oid", oid)
		}
	}

	// Insert dots between numbers
	var result strings.Builder
	for i, c := range oid {
		if i > 0 {
			result.WriteRune('.')
		}
		result.WriteRune(c)
	}

	formattedOID := result.String()
	slog.Debug("Formatted OID",
		"original", oid,
		"formatted", formattedOID)

	return formattedOID, nil
}
