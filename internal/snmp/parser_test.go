package snmp

import (
	"testing"

	"github.com/gosnmp/gosnmp"
)

func TestGetOIDName(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string",
			input:       "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "known OID with leading dot",
			input:       ".1.3.6.1.2.1.1.3.0",
			expected:    "sysUpTime.0",
			expectError: false,
		},
		{
			name:        "known OID without leading dot",
			input:       "1.3.6.1.2.1.1.3.0",
			expected:    "sysUpTime.0",
			expectError: false,
		},
		{
			name:        "known OID prefix with additional suffix",
			input:       "1.3.6.1.2.1.2.2.1.1.5",
			expected:    "ifIndex.5",
			expectError: false,
		},
		{
			name:        "unknown OID",
			input:       "1.2.3.4.5",
			expected:    "1.2.3.4.5",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := getOIDName(tt.input)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Errorf("getOIDName(%q) expected error but got none", tt.input)
			}
			if !tt.expectError && err != nil {
				t.Errorf("getOIDName(%q) unexpected error: %v", tt.input, err)
			}

			// If we don't expect an error, check the result
			if !tt.expectError {
				if result != tt.expected {
					t.Errorf("getOIDName(%q) = %q, want %q", tt.input, result, tt.expected)
				}
			}
		})
	}
}

func TestGetVariableType(t *testing.T) {
	tests := []struct {
		name     string
		varType  gosnmp.Asn1BER
		expected string
	}{
		{
			name:     "Integer",
			varType:  gosnmp.Integer,
			expected: "Integer",
		},
		{
			name:     "OctetString",
			varType:  gosnmp.OctetString,
			expected: "OctetString",
		},
		{
			name:     "ObjectIdentifier",
			varType:  gosnmp.ObjectIdentifier,
			expected: "ObjectIdentifier",
		},
		{
			name:     "IPAddress",
			varType:  gosnmp.IPAddress,
			expected: "IPAddress",
		},
		{
			name:     "Counter32",
			varType:  gosnmp.Counter32,
			expected: "Counter32",
		},
		{
			name:     "Gauge32",
			varType:  gosnmp.Gauge32,
			expected: "Gauge32",
		},
		{
			name:     "TimeTicks",
			varType:  gosnmp.TimeTicks,
			expected: "TimeTicks",
		},
		{
			name:     "Counter64",
			varType:  gosnmp.Counter64,
			expected: "Counter64",
		},
		{
			name:     "Null",
			varType:  gosnmp.Null,
			expected: "Null",
		},
		{
			name:     "NoSuchObject",
			varType:  gosnmp.NoSuchObject,
			expected: "NoSuchObject",
		},
		{
			name:     "NoSuchInstance",
			varType:  gosnmp.NoSuchInstance,
			expected: "NoSuchInstance",
		},
		{
			name:     "EndOfMibView",
			varType:  gosnmp.EndOfMibView,
			expected: "EndOfMibView",
		},
		{
			name:     "Unknown",
			varType:  gosnmp.Asn1BER(99),
			expected: "Unknown (99)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getVariableType(tt.varType)
			if result != tt.expected {
				t.Errorf("getVariableType(%v) = %q, want %q", tt.varType, result, tt.expected)
			}
		})
	}
}

func TestFormatVariableValue(t *testing.T) {
	tests := []struct {
		name     string
		varType  gosnmp.Asn1BER
		value    interface{}
		expected interface{}
	}{
		{
			name:     "TimeTicks",
			varType:  gosnmp.TimeTicks,
			value:    uint32(123456),
			expected: "20m34.56s (123456)",
		},
		{
			name:     "OctetString",
			varType:  gosnmp.OctetString,
			value:    []byte("test string"),
			expected: "test string",
		},
		{
			name:     "Integer",
			varType:  gosnmp.Integer,
			value:    int(42),
			expected: int(42),
		},
		{
			name:     "TimeTicks with non-uint32 value",
			varType:  gosnmp.TimeTicks,
			value:    "not a uint32",
			expected: "not a uint32",
		},
		{
			name:     "OctetString with non-byte-slice value",
			varType:  gosnmp.OctetString,
			value:    42,
			expected: 42,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := formatVariableValue(tt.varType, tt.value)

			// For string results, we need to check if they contain the expected value
			// because the exact format might vary (e.g., time duration formatting)
			if resultStr, ok := result.(string); ok {
				if expectedStr, ok := tt.expected.(string); ok {
					if resultStr != expectedStr {
						t.Errorf("formatVariableValue(%v, %v) = %q, want %q", tt.varType, tt.value, resultStr, expectedStr)
					}
					return
				}
			}

			// For non-string results, we can compare directly
			if result != tt.expected {
				t.Errorf("formatVariableValue(%v, %v) = %v, want %v", tt.varType, tt.value, result, tt.expected)
			}
		})
	}
}

func TestFormatOID(t *testing.T) {
	tests := []struct {
		name        string
		input       string
		expected    string
		expectError bool
	}{
		{
			name:        "empty string",
			input:       "",
			expected:    "",
			expectError: true,
		},
		{
			name:        "already formatted",
			input:       "1.3.6.1.4.1",
			expected:    "1.3.6.1.4.1",
			expectError: false,
		},
		{
			name:        "needs formatting",
			input:       "13614",
			expected:    "1.3.6.1.4",
			expectError: false,
		},
		{
			name:        "non-digit characters",
			input:       "1a3614",
			expected:    "",
			expectError: true,
		},
		{
			name:        "single digit",
			input:       "1",
			expected:    "1",
			expectError: false,
		},
		{
			name:        "long OID",
			input:       "1361412345",
			expected:    "1.3.6.1.4.1.2.3.4.5",
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := FormatOID(tt.input)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Errorf("FormatOID(%q) expected error but got none", tt.input)
			}
			if !tt.expectError && err != nil {
				t.Errorf("FormatOID(%q) unexpected error: %v", tt.input, err)
			}

			// If we don't expect an error, check the result
			if !tt.expectError {
				if result != tt.expected {
					t.Errorf("FormatOID(%q) = %q, want %q", tt.input, result, tt.expected)
				}
			}
		})
	}
}
