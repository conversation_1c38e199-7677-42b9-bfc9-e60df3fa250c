package snmp

import (
	"context"
	"log/slog"
	"net"
	"time"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/translator"

	"github.com/gosnmp/gosnmp"
)

// DefaultTrapHandler is the default implementation of TrapHandler
type DefaultTrapHandler struct {
	config  *config.Config
	parser  *TrapParser
	outputs []TrapOutput
}

// TrapOutput is an interface for handling trap output
type TrapOutput interface {
	Output(msg *TrapMessage) error
}

// NewDefaultTrapHandler creates a new DefaultTrapHandler
func NewDefaultTrapHandler(cfg *config.Config) (*DefaultTrapHandler, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	// Create OID translator
	oidTranslator, err := translator.NewOIDTranslator(cfg.SNMP.MIBPaths, 5*time.Second)
	if err != nil {
		slog.Warn("Failed to create OID translator, using fallback translation",
			"error", err)
		// Continue without translator - parser will use fallback
		return &DefaultTrapHandler{
			config: cfg,
			parser: NewTrapParser(),
		}, nil
	}

	slog.Info("OID translator initialized successfully",
		"mib_paths", cfg.SNMP.MIBPaths)

	return &DefaultTrapHandler{
		config: cfg,
		parser: NewTrapParserWithTranslator(oidTranslator),
	}, nil
}

// RegisterOutput registers an output handler
func (h *DefaultTrapHandler) RegisterOutput(output TrapOutput) error {
	// Validate input
	if err := errors.ValidateNotNil(output, "output"); err != nil {
		return err
	}

	h.outputs = append(h.outputs, output)
	return nil
}

// HandleTrap handles an SNMP trap
func (h *DefaultTrapHandler) HandleTrap(packet *gosnmp.SnmpPacket, addr *net.UDPAddr) error {
	// Validate input
	if err := errors.ValidateNotNil(packet, "packet"); err != nil {
		return err
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		return err
	}

	// Parse the trap
	msg, err := h.parser.ParseTrap(packet, addr)
	if err != nil {
		slog.Error("Failed to parse SNMP trap",
			"error", err,
			"source", addr.String())
		return err
	}

	// Log the trap
	slog.Info("Received SNMP trap",
		"source", msg.SourceIP,
		"community", msg.Community,
		"version", msg.Version,
		"trap_type", msg.TrapType,
		"oid", msg.OID,
		"variables", len(msg.Variables))

	// Send to all registered outputs
	for _, output := range h.outputs {
		if err := output.Output(msg); err != nil {
			slog.Error("Failed to output SNMP trap",
				"error", err,
				"source", msg.SourceIP)
		}
	}

	return nil
}

// ConsoleOutput is a simple output handler that logs to the console
type ConsoleOutput struct {
	format string
}

// NewConsoleOutput creates a new ConsoleOutput
func NewConsoleOutput(format string) (*ConsoleOutput, error) {
	// Validate input
	if err := errors.ValidateOneOf(format, []string{"text", "json", "logfmt"}, "format"); err != nil {
		return nil, err
	}

	return &ConsoleOutput{
		format: format,
	}, nil
}

// Output outputs a trap message to the console
func (o *ConsoleOutput) Output(msg *TrapMessage) error {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return err
	}

	switch o.format {
	case "json":
		// Log as JSON
		slog.Info("SNMP Trap", "trap", msg)
	default:
		// Log as structured log
		attrs := []slog.Attr{
			slog.String("source", msg.SourceIP),
			slog.String("trap_type", msg.TrapType),
			slog.String("oid", msg.OID),
		}

		// Add variables
		for _, v := range msg.Variables {
			attrs = append(attrs, slog.Any(v.Name, v.Value))
		}

		slog.LogAttrs(context.TODO(), slog.LevelInfo, "SNMP Trap", attrs...)
	}

	return nil
}
