package snmp

import (
	"net"

	"argus/internal/config"
	"argus/internal/errors"

	"github.com/gosnmp/gosnmp"
	"github.com/rs/zerolog/log"
)

// DefaultTrapHandler is the default implementation of TrapHandler
type DefaultTrapHandler struct {
	config  *config.Config
	parser  *TrapParser
	outputs []TrapOutput
}

// TrapOutput is an interface for handling trap output
type TrapOutput interface {
	Output(msg *TrapMessage) error
}

// NewDefaultTrapHandler creates a new DefaultTrapHandler
func NewDefaultTrapHandler(cfg *config.Config) (*DefaultTrapHandler, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	return &DefaultTrapHandler{
		config: cfg,
		parser: NewTrapParser(),
	}, nil
}

// RegisterOutput registers an output handler
func (h *DefaultTrapHandler) RegisterOutput(output TrapOutput) error {
	// Validate input
	if err := errors.ValidateNotNil(output, "output"); err != nil {
		return err
	}

	h.outputs = append(h.outputs, output)
	return nil
}

// HandleTrap handles an SNMP trap
func (h *DefaultTrapHandler) HandleTrap(packet *gosnmp.SnmpPacket, addr *net.UDPAddr) error {
	// Validate input
	if err := errors.ValidateNotNil(packet, "packet"); err != nil {
		return err
	}
	if err := errors.ValidateNotNil(addr, "addr"); err != nil {
		return err
	}

	// Parse the trap
	msg, err := h.parser.ParseTrap(packet, addr)
	if err != nil {
		log.Error().
			Err(err).
			Str("source", addr.String()).
			Msg("Failed to parse SNMP trap")
		return err
	}

	// Log the trap
	log.Info().
		Str("source", msg.SourceIP).
		Str("community", msg.Community).
		Str("version", msg.Version).
		Str("trap_type", msg.TrapType).
		Str("oid", msg.OID).
		Int("variables", len(msg.Variables)).
		Msg("Received SNMP trap")

	// Send to all registered outputs
	for _, output := range h.outputs {
		if err := output.Output(msg); err != nil {
			log.Error().
				Err(err).
				Str("source", msg.SourceIP).
				Msg("Failed to output SNMP trap")
		}
	}

	return nil
}

// ConsoleOutput is a simple output handler that logs to the console
type ConsoleOutput struct {
	format string
}

// NewConsoleOutput creates a new ConsoleOutput
func NewConsoleOutput(format string) (*ConsoleOutput, error) {
	// Validate input
	if err := errors.ValidateOneOf(format, []string{"text", "json", "logfmt"}, "format"); err != nil {
		return nil, err
	}

	return &ConsoleOutput{
		format: format,
	}, nil
}

// Output outputs a trap message to the console
func (o *ConsoleOutput) Output(msg *TrapMessage) error {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return err
	}

	switch o.format {
	case "json":
		// Log as JSON
		log.Info().
			Interface("trap", msg).
			Msg("SNMP Trap")
	default:
		// Log as structured log
		logEvent := log.Info().
			Str("source", msg.SourceIP).
			Str("trap_type", msg.TrapType).
			Str("oid", msg.OID)

		// Add variables
		for _, v := range msg.Variables {
			logEvent = logEvent.Interface(v.Name, v.Value)
		}

		logEvent.Msg("SNMP Trap")
	}

	return nil
}
