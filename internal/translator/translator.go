package translator

import (
	"context"
	"os/exec"
	"strings"
	"sync"
	"time"

	"argus/internal/errors"

	"github.com/rs/zerolog/log"
)

// OIDTranslator provides OID to name translation using snmptranslate
type OIDTranslator struct {
	mibPaths []string
	timeout  time.Duration
	cache    map[string]string
	cacheMu  sync.RWMutex
	maxCache int
}

// Translator is the interface for OID translation
type Translator interface {
	TranslateOID(ctx context.Context, oid string) (string, error)
	Close() error
}

// NewOIDTranslator creates a new OID translator
func NewOIDTranslator(mibPaths []string, timeout time.Duration) (*OIDTranslator, error) {
	// Validate input
	if len(mibPaths) == 0 {
		return nil, errors.New(errors.ValidationError, "mibPaths cannot be empty")
	}
	if timeout <= 0 {
		timeout = 5 * time.Second // Default timeout
	}

	// Check if snmptranslate is available
	if err := checkSnmpTranslateAvailable(); err != nil {
		return nil, errors.Wrap(err, errors.InternalError, "snmptranslate not available")
	}

	return &OIDTranslator{
		mibPaths: mibPaths,
		timeout:  timeout,
		cache:    make(map[string]string),
		maxCache: 1000, // Limit cache size
	}, nil
}

// TranslateOID translates an OID to a human-readable name
func (t *OIDTranslator) TranslateOID(ctx context.Context, oid string) (string, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(oid, "oid"); err != nil {
		return "", err
	}

	// Clean OID (remove leading dot if present)
	cleanOID := strings.TrimPrefix(oid, ".")

	// Check cache first
	if cached := t.getCached(cleanOID); cached != "" {
		log.Debug().
			Str("oid", oid).
			Str("name", cached).
			Msg("OID translation found in cache")
		return cached, nil
	}

	// Try to translate using snmptranslate
	name, err := t.translateWithSnmpTranslate(ctx, cleanOID)
	if err != nil {
		log.Warn().
			Err(err).
			Str("oid", oid).
			Msg("Failed to translate OID with snmptranslate, using fallback")

		// Fallback: try basic known OIDs or return original
		if fallback := t.getFallbackTranslation(cleanOID); fallback != "" {
			t.setCached(cleanOID, fallback)
			return fallback, nil
		}

		// Return original OID if all else fails
		return oid, nil
	}

	// Cache the result
	t.setCached(cleanOID, name)

	log.Debug().
		Str("oid", oid).
		Str("name", name).
		Msg("OID translated successfully")

	return name, nil
}

// translateWithSnmpTranslate calls the snmptranslate command
func (t *OIDTranslator) translateWithSnmpTranslate(ctx context.Context, oid string) (string, error) {
	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, t.timeout)
	defer cancel()

	// Build MIB path string
	mibPathStr := strings.Join(t.mibPaths, ":")

	// Build command arguments for symbolic output
	args := []string{
		"-m", "ALL",
		"-M", mibPathStr,
		"-OS", // Symbolic output
		oid,
	}

	// Execute snmptranslate command
	cmd := exec.CommandContext(ctx, "snmptranslate", args...)
	output, err := cmd.Output()
	if err != nil {
		if ctx.Err() == context.DeadlineExceeded {
			return "", errors.New(errors.InternalError, "snmptranslate command timed out")
		}
		return "", errors.Wrap(err, errors.InternalError, "snmptranslate command failed")
	}

	// Parse output
	result := strings.TrimSpace(string(output))
	if result == "" {
		return "", errors.New(errors.ParseError, "OID not found in MIBs")
	}

	// Check if we got a symbolic name (not just the numeric OID back)
	cleanInputOID := strings.TrimPrefix(oid, ".")
	cleanResult := strings.TrimPrefix(result, ".")

	// If the result is the same as input (just numeric), it means no translation was found
	if cleanResult == cleanInputOID {
		return "", errors.New(errors.ParseError, "OID not found in MIBs")
	}

	return result, nil
}

// getFallbackTranslation provides basic fallback translations for common OIDs
func (t *OIDTranslator) getFallbackTranslation(oid string) string {
	// Basic known OIDs for fallback
	knownOIDs := map[string]string{
		"1.3.6.1.2.1.1.3.0":     "sysUpTime.0",
		"1.3.6.1.6.3.1.1.4.1.0": "snmpTrapOID.0",
		"1.3.6.1.2.1.2.2.1.1":   "ifIndex",
		"1.3.6.1.2.1.2.2.1.2":   "ifDescr",
		"1.3.6.1.2.1.2.2.1.3":   "ifType",
		"1.3.6.1.2.1.2.2.1.7":   "ifAdminStatus",
		"1.3.6.1.2.1.2.2.1.8":   "ifOperStatus",
	}

	// Check for exact match
	if name, exists := knownOIDs[oid]; exists {
		return name
	}

	// Check for prefix match
	for prefix, name := range knownOIDs {
		if strings.HasPrefix(oid, prefix) {
			suffix := oid[len(prefix):]
			if strings.HasPrefix(suffix, ".") {
				return name + suffix
			} else if suffix != "" {
				return name + "." + suffix
			}
		}
	}

	return ""
}

// getCached retrieves a cached translation
func (t *OIDTranslator) getCached(oid string) string {
	t.cacheMu.RLock()
	defer t.cacheMu.RUnlock()
	return t.cache[oid]
}

// setCached stores a translation in cache
func (t *OIDTranslator) setCached(oid, name string) {
	t.cacheMu.Lock()
	defer t.cacheMu.Unlock()

	// Simple cache eviction: clear cache if it gets too large
	if len(t.cache) >= t.maxCache {
		// Clear half the cache (simple strategy)
		newCache := make(map[string]string)
		count := 0
		for k, v := range t.cache {
			if count < t.maxCache/2 {
				newCache[k] = v
				count++
			}
		}
		t.cache = newCache
		log.Debug().
			Int("cache_size", len(t.cache)).
			Msg("OID translation cache evicted")
	}

	t.cache[oid] = name
}

// Close cleans up the translator
func (t *OIDTranslator) Close() error {
	t.cacheMu.Lock()
	defer t.cacheMu.Unlock()

	// Clear cache
	t.cache = nil

	log.Debug().Msg("OID translator closed")
	return nil
}

// checkSnmpTranslateAvailable checks if snmptranslate command is available
func checkSnmpTranslateAvailable() error {
	cmd := exec.Command("snmptranslate", "-V")
	if err := cmd.Run(); err != nil {
		return errors.Wrap(err, errors.InternalError, "snmptranslate command not found or not executable")
	}
	return nil
}
