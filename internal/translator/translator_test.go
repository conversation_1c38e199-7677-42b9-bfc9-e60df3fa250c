package translator

import (
	"context"
	"strings"
	"testing"
	"time"
)

func TestNewOIDTranslator(t *testing.T) {
	tests := []struct {
		name        string
		mibPaths    []string
		timeout     time.Duration
		expectError bool
	}{
		{
			name:        "valid configuration",
			mibPaths:    []string{"/usr/share/snmp/mibs"},
			timeout:     5 * time.Second,
			expectError: false,
		},
		{
			name:        "empty mib paths",
			mibPaths:    []string{},
			timeout:     5 * time.Second,
			expectError: true,
		},
		{
			name:        "zero timeout gets default",
			mibPaths:    []string{"/usr/share/snmp/mibs"},
			timeout:     0,
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			translator, err := NewOIDTranslator(tt.mibPaths, tt.timeout)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				// Skip test if snmptranslate is not available
				if err.Error() == "snmptranslate not available: snmptranslate command not found or not executable" {
					t.Skip("snmptranslate not available, skipping test")
				}
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if translator == nil {
				t.Errorf("Expected translator but got nil")
				return
			}

			// Verify configuration
			if len(translator.mibPaths) != len(tt.mibPaths) {
				t.Errorf("Expected %d mib paths, got %d", len(tt.mibPaths), len(translator.mibPaths))
			}

			expectedTimeout := tt.timeout
			if expectedTimeout <= 0 {
				expectedTimeout = 5 * time.Second
			}
			if translator.timeout != expectedTimeout {
				t.Errorf("Expected timeout %v, got %v", expectedTimeout, translator.timeout)
			}

			// Clean up
			translator.Close()
		})
	}
}

func TestOIDTranslator_TranslateOID(t *testing.T) {
	// Skip if snmptranslate is not available
	if err := checkSnmpTranslateAvailable(); err != nil {
		t.Skip("snmptranslate not available, skipping test")
	}

	translator, err := NewOIDTranslator([]string{"/usr/share/snmp/mibs"}, 5*time.Second)
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}
	defer translator.Close()

	tests := []struct {
		name        string
		oid         string
		expectError bool
		expectOID   bool // true if we expect the original OID back (fallback)
	}{
		{
			name:        "empty OID",
			oid:         "",
			expectError: true,
		},
		{
			name:        "sysUpTime OID",
			oid:         "*******.*******.0",
			expectError: false,
			expectOID:   false,
		},
		{
			name:        "OID with leading dot",
			oid:         ".*******.*******.0",
			expectError: false,
			expectOID:   false,
		},
		{
			name:        "unknown OID",
			oid:         "*******.*******.9",
			expectError: false,
			expectOID:   false, // snmptranslate will translate at least the root (1 -> iso)
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			result, err := translator.TranslateOID(ctx, tt.oid)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if result == "" {
				t.Errorf("Expected non-empty result")
				return
			}

			if tt.expectOID {
				// Should return original OID for unknown OIDs
				if result != tt.oid {
					t.Errorf("Expected original OID %s, got %s", tt.oid, result)
				}
			} else {
				// Should return translated name (not the original OID)
				cleanOID := tt.oid
				if cleanOID[0] == '.' {
					cleanOID = cleanOID[1:]
				}
				if result == cleanOID || result == tt.oid {
					t.Errorf("Expected translated name, but got original OID: %s", result)
				}
				// For unknown OIDs, we should at least get some translation (like iso.2.3...)
				if !strings.Contains(result, "iso") && !strings.Contains(result, "sysUpTime") {
					t.Logf("Got translation: %s (this is acceptable)", result)
				}
			}
		})
	}
}

func TestOIDTranslator_Cache(t *testing.T) {
	// Skip if snmptranslate is not available
	if err := checkSnmpTranslateAvailable(); err != nil {
		t.Skip("snmptranslate not available, skipping test")
	}

	translator, err := NewOIDTranslator([]string{"/usr/share/snmp/mibs"}, 5*time.Second)
	if err != nil {
		t.Fatalf("Failed to create translator: %v", err)
	}
	defer translator.Close()

	ctx := context.Background()
	oid := "*******.*******.0"

	// First translation
	result1, err := translator.TranslateOID(ctx, oid)
	if err != nil {
		t.Fatalf("First translation failed: %v", err)
	}

	// Second translation should use cache
	result2, err := translator.TranslateOID(ctx, oid)
	if err != nil {
		t.Fatalf("Second translation failed: %v", err)
	}

	if result1 != result2 {
		t.Errorf("Cache inconsistency: first result %s, second result %s", result1, result2)
	}

	// Verify cache contains the entry
	cached := translator.getCached(oid)
	if cached == "" {
		// Try without leading dot
		cached = translator.getCached("*******.*******.0")
	}
	if cached == "" {
		t.Errorf("Expected OID to be cached")
	}
}

func TestOIDTranslator_FallbackTranslation(t *testing.T) {
	translator := &OIDTranslator{}

	tests := []struct {
		name     string
		oid      string
		expected string
	}{
		{
			name:     "sysUpTime exact match",
			oid:      "*******.*******.0",
			expected: "sysUpTime.0",
		},
		{
			name:     "ifIndex prefix match",
			oid:      "*******.2.1.2.2.1.1.5",
			expected: "ifIndex.5",
		},
		{
			name:     "unknown OID",
			oid:      "*******.5",
			expected: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := translator.getFallbackTranslation(tt.oid)
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}

func TestOIDTranslator_Close(t *testing.T) {
	translator := &OIDTranslator{
		cache: make(map[string]string),
	}

	// Add some cache entries
	translator.setCached("1.2.3", "test")

	err := translator.Close()
	if err != nil {
		t.Errorf("Unexpected error: %v", err)
	}

	// Cache should be cleared
	if translator.cache != nil {
		t.Errorf("Expected cache to be nil after close")
	}
}
