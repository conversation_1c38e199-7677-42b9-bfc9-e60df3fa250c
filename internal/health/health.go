package health

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"argus/internal/config"
	"argus/internal/errors"

	"github.com/rs/zerolog/log"
)

// Status represents the status of a component
type Status string

const (
	// StatusUp indicates that the component is functioning properly
	StatusUp Status = "UP"
	// StatusDown indicates that the component is not functioning properly
	StatusDown Status = "DOWN"
	// StatusUnknown indicates that the status of the component is unknown
	StatusUnknown Status = "UNKNOWN"
)

// ComponentStatus represents the status of a component
type ComponentStatus struct {
	Status  Status                 `json:"status"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// HealthStatus represents the overall health status of the application
type HealthStatus struct {
	Status     Status                     `json:"status"`
	Components map[string]ComponentStatus `json:"components,omitempty"`
	Timestamp  string                     `json:"timestamp"`
}

// Server represents a health check server
type Server struct {
	config     *config.HealthConfig
	httpServer *http.Server
	status     HealthStatus
	mu         sync.RWMutex
	components map[string]ComponentStatusFunc
}

// ComponentStatusFunc is a function that returns the status of a component
type ComponentStatusFunc func() ComponentStatus

// NewServer creates a new health check server
func NewServer(cfg *config.HealthConfig) (*Server, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	// Create server
	server := &Server{
		config: cfg,
		status: HealthStatus{
			Status:     StatusUp,
			Components: make(map[string]ComponentStatus),
			Timestamp:  time.Now().Format(time.RFC3339),
		},
		components: make(map[string]ComponentStatusFunc),
	}

	// Register default components
	server.RegisterComponent("server", func() ComponentStatus {
		return ComponentStatus{
			Status: StatusUp,
			Details: map[string]interface{}{
				"uptime": time.Since(time.Now()).String(),
			},
		}
	})

	return server, nil
}

// RegisterComponent registers a component with the health check server
func (s *Server) RegisterComponent(name string, statusFunc ComponentStatusFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.components[name] = statusFunc
}

// Start starts the health check server
func (s *Server) Start(ctx context.Context) error {
	// Validate input
	if err := errors.ValidateNotNil(ctx, "ctx"); err != nil {
		return err
	}

	// Create HTTP server
	addr := fmt.Sprintf("%s:%d", s.config.BindAddress, s.config.Port)
	mux := http.NewServeMux()
	mux.HandleFunc("/health", s.handleHealth)
	mux.HandleFunc("/health/status", s.handleHealthStatus)

	s.httpServer = &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	// Start HTTP server in a goroutine
	go func() {
		log.Info().
			Str("address", addr).
			Msg("Health check server started")

		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Error().
				Err(err).
				Msg("Health check server error")
		}
	}()

	return nil
}

// Stop stops the health check server
func (s *Server) Stop() error {
	if s.httpServer != nil {
		// Create a context with a timeout for shutdown
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		// Shutdown the server
		if err := s.httpServer.Shutdown(ctx); err != nil {
			return errors.Wrap(err, errors.NetworkError, "Failed to shutdown health check server")
		}

		log.Info().Msg("Health check server stopped")
	}

	return nil
}

// handleHealth handles the /health endpoint
func (s *Server) handleHealth(w http.ResponseWriter, r *http.Request) {
	// Update status
	s.updateStatus()

	// Set content type
	w.Header().Set("Content-Type", "application/json")

	// Set status code based on health status
	if s.status.Status == StatusUp {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	// Write response
	response := map[string]string{
		"status": string(s.status.Status),
	}
	json.NewEncoder(w).Encode(response)
}

// handleHealthStatus handles the /health/status endpoint
func (s *Server) handleHealthStatus(w http.ResponseWriter, r *http.Request) {
	// Update status
	s.updateStatus()

	// Set content type
	w.Header().Set("Content-Type", "application/json")

	// Set status code based on health status
	if s.status.Status == StatusUp {
		w.WriteHeader(http.StatusOK)
	} else {
		w.WriteHeader(http.StatusServiceUnavailable)
	}

	// Write response
	json.NewEncoder(w).Encode(s.status)
}

// updateStatus updates the health status
func (s *Server) updateStatus() {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Update timestamp
	s.status.Timestamp = time.Now().Format(time.RFC3339)

	// Update component statuses
	for name, statusFunc := range s.components {
		s.status.Components[name] = statusFunc()
	}

	// Update overall status
	s.status.Status = StatusUp
	for _, component := range s.status.Components {
		if component.Status == StatusDown {
			s.status.Status = StatusDown
			break
		}
		if component.Status == StatusUnknown && s.status.Status != StatusDown {
			s.status.Status = StatusUnknown
		}
	}
}
