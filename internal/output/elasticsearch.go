package output

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/snmp"

	"github.com/elastic/go-elasticsearch/v8"
	"github.com/elastic/go-elasticsearch/v8/esapi"
)

// ElasticsearchOutput handles sending SNMP trap messages to Elasticsearch
type ElasticsearchOutput struct {
	client    *elasticsearch.Client
	config    *config.ElasticsearchConfig
	indexName string
}

// NewElasticsearchOutput creates a new Elasticsearch output handler
func NewElasticsearchOutput(cfg *config.ElasticsearchConfig) (*ElasticsearchOutput, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "config"); err != nil {
		return nil, err
	}

	if err := errors.ValidateNotEmpty(cfg.URL, "url"); err != nil {
		return nil, err
	}

	if err := errors.ValidateNotEmpty(cfg.Index, "index"); err != nil {
		return nil, err
	}

	// Create Elasticsearch client configuration
	esCfg := elasticsearch.Config{
		Addresses: []string{cfg.URL},
	}

	// Add authentication if configured
	if cfg.Username != "" && cfg.Password != "" {
		esCfg.Username = cfg.Username
		esCfg.Password = cfg.Password
	}

	// Add API key authentication if configured
	if cfg.APIKey != "" {
		esCfg.APIKey = cfg.APIKey
	}

	// Create client
	client, err := elasticsearch.NewClient(esCfg)
	if err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to create Elasticsearch client")
	}

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	res, err := client.Info(client.Info.WithContext(ctx))
	if err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to connect to Elasticsearch")
	}
	defer res.Body.Close()

	if res.IsError() {
		return nil, errors.New(errors.ConfigError, "Elasticsearch connection test failed").
			WithMetadata("status", res.Status()).
			WithMetadata("url", cfg.URL)
	}

	slog.Info("Elasticsearch client initialized successfully",
		"url", cfg.URL,
		"index", cfg.Index)

	return &ElasticsearchOutput{
		client:    client,
		config:    cfg,
		indexName: cfg.Index,
	}, nil
}

// Output sends a trap message to Elasticsearch
func (e *ElasticsearchOutput) Output(msg *snmp.TrapMessage) error {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return err
	}

	// Create document for Elasticsearch
	doc := e.createDocument(msg)

	// Convert to JSON
	docJSON, err := json.Marshal(doc)
	if err != nil {
		return errors.Wrap(err, errors.ProcessingError, "Failed to marshal document to JSON")
	}

	// Generate document ID (optional - Elasticsearch can auto-generate)
	docID := e.generateDocumentID(msg)

	// Create index request
	req := esapi.IndexRequest{
		Index:      e.indexName,
		DocumentID: docID,
		Body:       bytes.NewReader(docJSON),
		Refresh:    "false", // Don't force refresh for performance
	}

	// Execute request with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	res, err := req.Do(ctx, e.client)
	if err != nil {
		return errors.Wrap(err, errors.ProcessingError, "Failed to index document in Elasticsearch").
			WithMetadata("index", e.indexName).
			WithMetadata("source", msg.SourceIP)
	}
	defer res.Body.Close()

	if res.IsError() {
		return errors.New(errors.ProcessingError, "Elasticsearch indexing failed").
			WithMetadata("status", res.Status()).
			WithMetadata("index", e.indexName).
			WithMetadata("source", msg.SourceIP)
	}

	slog.Debug("SNMP trap indexed in Elasticsearch",
		"index", e.indexName,
		"source", msg.SourceIP,
		"trap_oid", msg.OID,
		"document_id", docID)

	return nil
}

// createDocument creates an Elasticsearch document from a trap message
func (e *ElasticsearchOutput) createDocument(msg *snmp.TrapMessage) map[string]interface{} {
	doc := map[string]interface{}{
		"@timestamp":  time.Now().UTC().Format(time.RFC3339),
		"source_ip":   msg.SourceIP,
		"community":   msg.Community,
		"version":     msg.Version,
		"trap_type":   msg.TrapType,
		"trap_oid":    msg.OID,
		"variables":   make([]map[string]interface{}, len(msg.Variables)),
		"agent_addr":  msg.AgentAddress,
		"enterprise":  msg.Enterprise,
		"generic_trap": msg.GenericTrap,
		"specific_trap": msg.SpecificTrap,
		"timestamp":   msg.Timestamp,
	}

	// Add variables
	for i, variable := range msg.Variables {
		doc["variables"].([]map[string]interface{})[i] = map[string]interface{}{
			"oid":   variable.OID,
			"name":  variable.Name,
			"type":  variable.Type,
			"value": variable.Value,
		}
	}

	// Add custom fields if configured
	if e.config.CustomFields != nil {
		for key, value := range e.config.CustomFields {
			doc[key] = value
		}
	}

	return doc
}

// generateDocumentID generates a unique document ID for the trap message
func (e *ElasticsearchOutput) generateDocumentID(msg *snmp.TrapMessage) string {
	// Create a unique ID based on source IP, timestamp, and OID
	timestamp := time.Now().UTC().Format("20060102150405.000000")
	sourceIP := strings.ReplaceAll(msg.SourceIP, ".", "_")
	oid := strings.ReplaceAll(msg.OID, ".", "_")
	
	return fmt.Sprintf("%s_%s_%s", sourceIP, timestamp, oid)
}

// Close closes the Elasticsearch output handler
func (e *ElasticsearchOutput) Close() error {
	// The Elasticsearch client doesn't require explicit closing
	// but we can log that the output is being closed
	slog.Info("Elasticsearch output handler closed",
		"index", e.indexName,
		"url", e.config.URL)
	return nil
}

// Health checks the health of the Elasticsearch connection
func (e *ElasticsearchOutput) Health() error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	res, err := e.client.Cluster.Health(
		e.client.Cluster.Health.WithContext(ctx),
		e.client.Cluster.Health.WithWaitForStatus("yellow"),
		e.client.Cluster.Health.WithTimeout("5s"),
	)
	if err != nil {
		return errors.Wrap(err, errors.ProcessingError, "Elasticsearch health check failed")
	}
	defer res.Body.Close()

	if res.IsError() {
		return errors.New(errors.ProcessingError, "Elasticsearch cluster unhealthy").
			WithMetadata("status", res.Status())
	}

	return nil
}

// GetIndexName returns the configured index name
func (e *ElasticsearchOutput) GetIndexName() string {
	return e.indexName
}

// GetURL returns the configured Elasticsearch URL
func (e *ElasticsearchOutput) GetURL() string {
	return e.config.URL
}
