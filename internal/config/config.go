package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"argus/internal/errors"

log/slog
	"github.com/spf13/viper"
)

// Config represents the application configuration
type Config struct {
	Server    ServerConfig   `mapstructure:"server"`
	SNMP      SNMPConfig     `mapstructure:"snmp"`
	Output    OutputConfig   `mapstructure:"output"`
	Templates TemplateConfig `mapstructure:"templates"`
	Logging   LoggingConfig  `mapstructure:"logging"`
	Health    HealthConfig   `mapstructure:"health"`
}

// ServerConfig contains server-related configuration
type ServerConfig struct {
	Port        int    `mapstructure:"port"`
	BindAddress string `mapstructure:"bind_address"`
}

// SNMPConfig contains SNMP-related configuration
type SNMPConfig struct {
	Community string   `mapstructure:"community"`
	Version   string   `mapstructure:"version"`
	MIBPaths  []string `mapstructure:"mib_paths"`
}

// OutputConfig contains output-related configuration
type OutputConfig struct {
	Stdout        StdoutConfig        `mapstructure:"stdout"`
	Elasticsearch ElasticsearchConfig `mapstructure:"elasticsearch"`
}

// StdoutConfig contains stdout output configuration
type StdoutConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Format  string `mapstructure:"format"`
}

// ElasticsearchConfig contains Elasticsearch output configuration
type ElasticsearchConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	URL      string `mapstructure:"url"`
	Index    string `mapstructure:"index"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

// TemplateConfig contains template-related configuration
type TemplateConfig struct {
	Path    string `mapstructure:"path"`
	Default string `mapstructure:"default"`
}

// LoggingConfig contains logging-related configuration
type LoggingConfig struct {
	Level string `mapstructure:"level"`
	File  string `mapstructure:"file"`
}

// HealthConfig contains health check-related configuration
type HealthConfig struct {
	Enabled     bool   `mapstructure:"enabled"`
	Port        int    `mapstructure:"port"`
	BindAddress string `mapstructure:"bind_address"`
}

// LoadConfig loads the configuration from file, environment variables, and command-line flags
func LoadConfig(configFile string) (*Config, error) {
	// Validate input
	if err := errors.ValidateNotEmpty(configFile, "configFile"); err != nil {
		return nil, err
	}

	// Set default configuration values
	setDefaults()

	// Load configuration from file
	if err := loadConfigFile(configFile); err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to load configuration file")
	}

	// Load configuration from environment variables
	loadEnvVars()

	// Parse configuration into struct
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, errors.Wrap(err, errors.ConfigError, "Failed to unmarshal configuration")
	}

	// Validate configuration
	if err := validateConfig(&config); err != nil {
		return nil, err
	}

	return &config, nil
}

// setDefaults sets default configuration values
func setDefaults() {
	// Server defaults
	viper.SetDefault("server.port", 162)
	viper.SetDefault("server.bind_address", "0.0.0.0")

	// SNMP defaults
	viper.SetDefault("snmp.community", "public")
	viper.SetDefault("snmp.version", "2c")
	viper.SetDefault("snmp.mib_paths", []string{"/usr/share/snmp/mibs"})

	// Output defaults
	viper.SetDefault("output.stdout.enabled", true)
	viper.SetDefault("output.stdout.format", "text")
	viper.SetDefault("output.elasticsearch.enabled", false)
	viper.SetDefault("output.elasticsearch.url", "http://localhost:9200")
	viper.SetDefault("output.elasticsearch.index", "argus-traps")

	// Template defaults
	viper.SetDefault("templates.path", "./templates")
	viper.SetDefault("templates.default", "default.cue")

	// Logging defaults
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.file", "")

	// Health check defaults
	viper.SetDefault("health.enabled", true)
	viper.SetDefault("health.port", 8080)
	viper.SetDefault("health.bind_address", "0.0.0.0")
}

// loadConfigFile loads configuration from a file
func loadConfigFile(configFile string) error {
	viper.SetConfigFile(configFile)
	return viper.ReadInConfig()
}

// loadEnvVars loads configuration from environment variables
func loadEnvVars() {
	viper.SetEnvPrefix("ARGUS")
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	viper.AutomaticEnv()
}

// validateConfig validates the configuration
func validateConfig(config *Config) error {
	// Validate server configuration
	if err := errors.ValidatePositive(config.Server.Port, "server.port"); err != nil {
		return err
	}
	if err := errors.ValidateNotEmpty(config.Server.BindAddress, "server.bind_address"); err != nil {
		return err
	}

	// Validate SNMP configuration
	if err := errors.ValidateNotEmpty(config.SNMP.Community, "snmp.community"); err != nil {
		return err
	}
	if err := errors.ValidateOneOf(config.SNMP.Version, []string{"1", "2c", "3"}, "snmp.version"); err != nil {
		return err
	}
	if len(config.SNMP.MIBPaths) == 0 {
		return errors.New(errors.ConfigError, "snmp.mib_paths cannot be empty")
	}

	// Validate MIB paths existence
	for _, path := range config.SNMP.MIBPaths {
		if _, err := os.Stat(path); os.IsNotExist(err) {
			slog.Warn("MIB path does not exist", "path", path)
		}
	}

	// Validate output configuration
	if !config.Output.Stdout.Enabled && !config.Output.Elasticsearch.Enabled {
		return errors.New(errors.ConfigError, "At least one output must be enabled")
	}
	if config.Output.Stdout.Enabled {
		if err := errors.ValidateOneOf(config.Output.Stdout.Format, []string{"text", "json", "logfmt"}, "output.stdout.format"); err != nil {
			return err
		}
	}
	if config.Output.Elasticsearch.Enabled {
		if err := errors.ValidateNotEmpty(config.Output.Elasticsearch.URL, "output.elasticsearch.url"); err != nil {
			return err
		}
		if err := errors.ValidateNotEmpty(config.Output.Elasticsearch.Index, "output.elasticsearch.index"); err != nil {
			return err
		}
		// Validate username and password if provided
		if config.Output.Elasticsearch.Username != "" && config.Output.Elasticsearch.Password == "" {
			return errors.New(errors.ConfigError, "Elasticsearch password must be provided when username is set")
		}
	}

	// Validate template configuration
	if err := errors.ValidateNotEmpty(config.Templates.Path, "templates.path"); err != nil {
		return err
	}
	// Check if template path exists
	if _, err := os.Stat(config.Templates.Path); os.IsNotExist(err) {
		return errors.Wrap(err, errors.ConfigError, "Template path does not exist").
			WithMetadata("path", config.Templates.Path)
	}

	if err := errors.ValidateNotEmpty(config.Templates.Default, "templates.default"); err != nil {
		return err
	}

	// Check if default template exists
	defaultTemplatePath := filepath.Join(config.Templates.Path, config.Templates.Default)
	if !strings.HasSuffix(defaultTemplatePath, ".cue") {
		defaultTemplatePath += ".cue"
	}
	if _, err := os.Stat(defaultTemplatePath); os.IsNotExist(err) {
		return errors.Wrap(err, errors.ConfigError, "Default template does not exist").
			WithMetadata("path", defaultTemplatePath)
	}

	// Validate logging configuration
	if config.Logging.Level != "" {
		// Validate log level using slog levels
		validLevels := []string{"debug", "info", "warn", "error"}
		isValid := false
		for _, level := range validLevels {
			if config.Logging.Level == level {
				isValid = true
				break
			}
		}
		if !isValid {
			return errors.New(errors.ConfigError, "Invalid log level").
				WithMetadata("level", config.Logging.Level).
				WithMetadata("valid_levels", validLevels)
		}
	}

	if config.Logging.File != "" {
		// Check if the directory exists
		dir := filepath.Dir(config.Logging.File)
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			return errors.Wrap(err, errors.ConfigError, "Log file directory does not exist").
				WithMetadata("directory", dir)
		}
	}

	// Validate health check configuration
	if config.Health.Enabled {
		if err := errors.ValidatePositive(config.Health.Port, "health.port"); err != nil {
			return err
		}
		if err := errors.ValidateNotEmpty(config.Health.BindAddress, "health.bind_address"); err != nil {
			return err
		}
	}

	return nil
}

// UpdateConfigFromFlags updates the configuration with command-line flags
func UpdateConfigFromFlags(config *Config, port int, mibPaths string, outputType string, outputFormat string) error {
	// Validate input
	if err := errors.ValidateNotNil(config, "config"); err != nil {
		return err
	}

	if port != 0 {
		if err := errors.ValidatePositive(port, "port"); err != nil {
			return err
		}
		config.Server.Port = port
	}

	if mibPaths != "" {
		paths := strings.Split(mibPaths, ",")
		for i, path := range paths {
			if err := errors.ValidateNotEmpty(path, fmt.Sprintf("mibPaths[%d]", i)); err != nil {
				return err
			}
		}
		config.SNMP.MIBPaths = paths
	}

	if outputType != "" {
		if err := errors.ValidateOneOf(outputType, []string{"stdout", "elasticsearch", "both"}, "outputType"); err != nil {
			return err
		}

		switch outputType {
		case "stdout":
			config.Output.Stdout.Enabled = true
			config.Output.Elasticsearch.Enabled = false
		case "elasticsearch":
			config.Output.Stdout.Enabled = false
			config.Output.Elasticsearch.Enabled = true
		case "both":
			config.Output.Stdout.Enabled = true
			config.Output.Elasticsearch.Enabled = true
		}
	}

	if outputFormat != "" {
		if err := errors.ValidateOneOf(outputFormat, []string{"text", "json", "logfmt"}, "outputFormat"); err != nil {
			return err
		}
		config.Output.Stdout.Format = outputFormat
	}

	return nil
}
