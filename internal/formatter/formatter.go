package formatter

import (
	"context"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"argus/internal/config"
	"argus/internal/errors"
	"argus/internal/snmp"

	"cuelang.org/go/cue"
	"cuelang.org/go/cue/cuecontext"
	"cuelang.org/go/cue/load"
	"github.com/rs/zerolog/log"
)

// TemplateEngine is responsible for loading and applying Cue templates
type TemplateEngine struct {
	config          *config.Config
	templatePath    string
	defaultTemplate string
	cache           *TemplateCache
	cueContext      *cue.Context
	mu              sync.RWMutex
}

// TemplateCache caches templates for performance
type TemplateCache struct {
	templates map[string]*Template
	mu        sync.RWMutex
}

// Template represents a Cue template
type Template struct {
	Name     string
	Path     string
	Value    cue.Value
	LoadTime time.Time
}

// NewTemplateEngine creates a new template engine
func NewTemplateEngine(cfg *config.Config) (*TemplateEngine, error) {
	// Validate input
	if err := errors.ValidateNotNil(cfg, "cfg"); err != nil {
		return nil, err
	}

	// Create Cue context
	cueCtx := cuecontext.New()

	// Create template cache
	cache := &TemplateCache{
		templates: make(map[string]*Template),
	}

	// Create template engine
	engine := &TemplateEngine{
		config:          cfg,
		templatePath:    cfg.Templates.Path,
		defaultTemplate: cfg.Templates.Default,
		cache:           cache,
		cueContext:      cueCtx,
	}

	// Load default template
	if _, err := engine.GetTemplate(engine.defaultTemplate); err != nil {
		return nil, errors.Wrap(err, errors.TemplateError, "Failed to load default template")
	}

	// Preload common templates
	if err := engine.PreloadTemplates(); err != nil {
		log.Warn().Err(err).Msg("Failed to preload templates")
	}

	return engine, nil
}

// PreloadTemplates preloads common templates
func (e *TemplateEngine) PreloadTemplates() error {
	// Get list of template files
	files, err := ioutil.ReadDir(e.templatePath)
	if err != nil {
		return errors.Wrap(err, errors.TemplateError, "Failed to read template directory")
	}

	// Load each template
	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".cue") {
			if _, err := e.GetTemplate(file.Name()); err != nil {
				log.Warn().
					Err(err).
					Str("template", file.Name()).
					Msg("Failed to preload template")
			}
		}
	}

	return nil
}

// GetTemplate gets a template by name, loading it if necessary
func (e *TemplateEngine) GetTemplate(name string) (*Template, error) {
	// Check if template is in cache
	e.cache.mu.RLock()
	template, ok := e.cache.templates[name]
	e.cache.mu.RUnlock()

	if ok {
		log.Debug().
			Str("template", name).
			Time("load_time", template.LoadTime).
			Msg("Template found in cache")
		return template, nil
	}

	// Load template
	e.mu.Lock()
	defer e.mu.Unlock()

	// Check again in case another goroutine loaded it
	e.cache.mu.RLock()
	template, ok = e.cache.templates[name]
	e.cache.mu.RUnlock()

	if ok {
		return template, nil
	}

	// Load template from file
	path := filepath.Join(e.templatePath, name)
	if !strings.HasSuffix(path, ".cue") {
		path += ".cue"
	}

	// Check if file exists
	if _, err := os.Stat(path); os.IsNotExist(err) {
		return nil, errors.New(errors.TemplateError, fmt.Sprintf("Template file not found: %s", path))
	}

	// Load template
	log.Debug().
		Str("template", name).
		Str("path", path).
		Msg("Loading template from file")

	// Use Cue's load package to load the template
	instances := load.Instances([]string{path}, nil)
	if len(instances) == 0 {
		return nil, errors.New(errors.TemplateError, "No instances loaded")
	}

	instance := instances[0]
	if instance.Err != nil {
		return nil, errors.Wrap(instance.Err, errors.TemplateError, "Failed to load template")
	}

	// Build the Cue value
	value := e.cueContext.BuildInstance(instance)
	if value.Err() != nil {
		return nil, errors.Wrap(value.Err(), errors.TemplateError, "Failed to build template")
	}

	// Create template
	template = &Template{
		Name:     name,
		Path:     path,
		Value:    value,
		LoadTime: time.Now(),
	}

	// Add to cache
	e.cache.mu.Lock()
	e.cache.templates[name] = template
	e.cache.mu.Unlock()

	log.Debug().
		Str("template", name).
		Str("path", path).
		Msg("Template loaded and cached")

	return template, nil
}

// ApplyTemplate applies a template to a trap message
func (e *TemplateEngine) ApplyTemplate(ctx context.Context, msg *snmp.TrapMessage) (map[string]interface{}, error) {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return nil, err
	}

	// Determine template name based on trap type
	templateName := e.defaultTemplate
	if msg.TrapType != "" {
		// Extract trap type from OID
		parts := strings.Split(msg.OID, "::")
		if len(parts) > 1 {
			trapType := parts[1]
			// Check if a template exists for this trap type
			if _, err := e.GetTemplate(trapType + ".cue"); err == nil {
				templateName = trapType + ".cue"
			}
		}
	}

	// Get template
	template, err := e.GetTemplate(templateName)
	if err != nil {
		return nil, errors.Wrap(err, errors.TemplateError, "Failed to get template")
	}

	// Convert trap message to map
	data := map[string]interface{}{
		"timestamp": msg.Timestamp.Format(time.RFC3339),
		"source_ip": msg.SourceIP,
		"community": msg.Community,
		"version":   msg.Version,
		"trap_type": msg.TrapType,
		"oid":       msg.OID,
		"variables": msg.Variables,
		"raw_data":  msg.RawData,
	}

	// Create Cue value from data
	value := e.cueContext.Encode(data)
	if value.Err() != nil {
		return nil, errors.Wrap(value.Err(), errors.TemplateError, "Failed to encode data")
	}

	// Unify with template
	result := template.Value.Unify(value)
	if result.Err() != nil {
		return nil, errors.Wrap(result.Err(), errors.TemplateError, "Failed to unify template with data")
	}

	// Extract result
	var output map[string]interface{}
	if err := result.Decode(&output); err != nil {
		return nil, errors.Wrap(err, errors.TemplateError, "Failed to decode result")
	}

	return output, nil
}

// FormatterOutput is an output handler that formats trap messages using templates
type FormatterOutput struct {
	engine *TemplateEngine
	output snmp.TrapOutput
}

// NewFormatterOutput creates a new formatter output
func NewFormatterOutput(engine *TemplateEngine, output snmp.TrapOutput) (*FormatterOutput, error) {
	// Validate input
	if err := errors.ValidateNotNil(engine, "engine"); err != nil {
		return nil, err
	}
	if err := errors.ValidateNotNil(output, "output"); err != nil {
		return nil, err
	}

	return &FormatterOutput{
		engine: engine,
		output: output,
	}, nil
}

// Output formats a trap message using a template and sends it to the output
func (o *FormatterOutput) Output(msg *snmp.TrapMessage) error {
	// Validate input
	if err := errors.ValidateNotNil(msg, "msg"); err != nil {
		return err
	}

	// Apply template
	ctx := context.Background()
	result, err := o.engine.ApplyTemplate(ctx, msg)
	if err != nil {
		return errors.Wrap(err, errors.TemplateError, "Failed to apply template")
	}

	// Create new trap message with formatted data
	formattedMsg := &snmp.TrapMessage{
		SourceIP:  msg.SourceIP,
		Community: msg.Community,
		Version:   msg.Version,
		TrapType:  msg.TrapType,
		OID:       msg.OID,
		Variables: msg.Variables,
		Timestamp: msg.Timestamp,
		RawData:   result,
	}

	// Send to output
	return o.output.Output(formattedMsg)
}
